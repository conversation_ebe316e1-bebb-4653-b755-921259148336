-- ██╗   ██╗███████╗██╗  ██╗██╗ ██████╗██╗     ███████╗    ██╗  ██╗██╗   ██╗██████╗ 
-- ██║   ██║██╔════╝██║  ██║██║██╔════╝██║     ██╔════╝    ██║  ██║██║   ██║██╔══██╗
-- ██║   ██║█████╗  ███████║██║██║     ██║     █████╗      ███████║██║   ██║██║  ██║
-- ╚██╗ ██╔╝██╔══╝  ██╔══██║██║██║     ██║     ██╔══╝      ██╔══██║██║   ██║██║  ██║
--  ╚████╔╝ ███████╗██║  ██║██║╚██████╗███████╗███████╗    ██║  ██║╚██████╔╝██████╔╝
--   ╚═══╝  ╚══════╝╚═╝  ╚═╝╚═╝ ╚═════╝╚══════╝╚══════╝    ╚═╝  ╚═╝ ╚═════╝ ╚═════╝ 
-- 
-- Valic HUD - Vehicle HUD Module
-- Author: Valic
-- Description: Vehicle speedometer and related functionality

local QBCore = exports['qb-core']:GetCoreObject()

-- Vehicle state
local currentVehicle = nil
local isInVehicle = false
local isDriver = false
local vehicleHudActive = false

-- Update intervals
local VEHICLE_UPDATE_INTERVAL = 100
local SEATBELT_CHECK_INTERVAL = 500

-- Seatbelt state
local seatbeltOn = false
local seatbeltSound = false

-- Initialize Vehicle HUD
RegisterNetEvent('valic_hud:client:setupVehicleHUD', function()
    CreateThread(function()
        while true do
            if vehicleHudActive and currentVehicle then
                UpdateVehicleHUD()
            end
            Wait(VEHICLE_UPDATE_INTERVAL)
        end
    end)
    
    CreateThread(function()
        while true do
            if isInVehicle and isDriver then
                CheckSeatbelt()
            end
            Wait(SEATBELT_CHECK_INTERVAL)
        end
    end)
end)

-- Vehicle enter/exit events
RegisterNetEvent('valic_hud:client:enteredVehicle', function(vehicle)
    currentVehicle = vehicle
    isInVehicle = true
    isDriver = GetPedInVehicleSeat(vehicle, -1) == PlayerPedId()
    vehicleHudActive = true
    seatbeltOn = false
    
    SendNUIMessage({
        action = 'showVehicleHUD',
        data = {
            show = true,
            isDriver = isDriver
        }
    })
end)

RegisterNetEvent('valic_hud:client:exitedVehicle', function(vehicle)
    currentVehicle = nil
    isInVehicle = false
    isDriver = false
    vehicleHudActive = false
    seatbeltOn = false
    
    SendNUIMessage({
        action = 'showVehicleHUD',
        data = {
            show = false
        }
    })
end)

-- Update vehicle HUD data
function UpdateVehicleHUD()
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        return
    end
    
    local vehicleData = ValicHUD.Vehicle.GetVehicleData(currentVehicle)
    if not vehicleData then return end
    
    -- Convert speed based on measurement system
    local speedMph = math.floor(vehicleData.speed * 2.236936)
    local speedKmh = math.floor(vehicleData.speed * 3.6)
    
    local hudData = {
        speed = {
            mph = speedMph,
            kmh = speedKmh
        },
        rpm = math.floor(vehicleData.rpm * 100),
        fuel = math.floor(vehicleData.fuel),
        engineHealth = math.floor(vehicleData.engineHealth / 10),
        bodyHealth = math.floor(vehicleData.bodyHealth / 10),
        gear = vehicleData.gear,
        engineOn = ValicHUD.Vehicle.IsEngineOn(currentVehicle),
        seatbelt = seatbeltOn,
        isDriver = isDriver,
        vehicleClass = ValicHUD.Vehicle.GetVehicleClass(currentVehicle)
    }
    
    -- Add lights status
    hudData.lights = {
        headlights = IsVehicleHeadlightOn(currentVehicle),
        highbeams = IsVehicleHighbeamOn(currentVehicle),
        indicators = {
            left = GetVehicleIndicatorLights(currentVehicle) == 1 or GetVehicleIndicatorLights(currentVehicle) == 3,
            right = GetVehicleIndicatorLights(currentVehicle) == 2 or GetVehicleIndicatorLights(currentVehicle) == 3
        }
    }
    
    -- Add doors status
    hudData.doors = {}
    for i = 0, 7 do
        hudData.doors[i] = GetVehicleDoorAngleRatio(currentVehicle, i) > 0.1
    end
    
    SendNUIMessage({
        action = 'updateVehicleHUD',
        data = hudData
    })
end

-- Seatbelt functionality
function CheckSeatbelt()
    if not currentVehicle or not isDriver then return end
    
    local speed = GetEntitySpeed(currentVehicle) * 3.6 -- Convert to km/h
    
    -- Seatbelt warning
    if speed > 50 and not seatbeltOn and not seatbeltSound then
        seatbeltSound = true
        -- Play seatbelt warning sound
        PlaySoundFrontend(-1, "Beep_Red", "DLC_HEIST_HACKING_SNAKE_SOUNDS", true)
        
        CreateThread(function()
            Wait(5000)
            seatbeltSound = false
        end)
    end
end

-- Seatbelt toggle
RegisterCommand('seatbelt', function()
    if not isInVehicle or not isDriver then
        return
    end
    
    seatbeltOn = not seatbeltOn
    
    -- Play seatbelt sound
    if seatbeltOn then
        PlaySoundFrontend(-1, "CONFIRM_BEEP", "HUD_MINI_GAME_SOUNDSET", false)
    else
        PlaySoundFrontend(-1, "CANCEL", "HUD_MINI_GAME_SOUNDSET", false)
    end
    
    -- Update HUD
    SendNUIMessage({
        action = 'updateSeatbelt',
        data = {
            seatbelt = seatbeltOn
        }
    })
    
    -- Notify other scripts
    TriggerEvent('seatbelt:client:ToggleSeatbelt', seatbeltOn)
end)

-- Engine toggle
RegisterCommand('engine', function()
    if not isInVehicle or not isDriver then
        QBCore.Functions.Notify(locale('YOU_NEED_TO_BE_DRIVER_ENGINE'), 'error')
        return
    end
    
    local engineOn = GetIsVehicleEngineRunning(currentVehicle)
    SetVehicleEngineOn(currentVehicle, not engineOn, false, true)
    
    if not engineOn then
        SetVehicleUndriveable(currentVehicle, false)
    end
end)

-- Vehicle actions
RegisterNetEvent('valic_hud:client:giveKeys', function()
    if not isInVehicle then return end
    
    local closestPlayer, distance = QBCore.Functions.GetClosestPlayer()
    if closestPlayer == -1 or distance > 3.0 then
        QBCore.Functions.Notify(locale('NO_ONE_TO_GIVE_KEYS'), 'error')
        return
    end
    
    local plate = QBCore.Functions.GetPlate(currentVehicle)
    TriggerServerEvent('qb-vehiclekeys:server:GiveVehicleKeys', GetPlayerServerId(closestPlayer), plate)
    QBCore.Functions.Notify(locale('GAVE_KEYS'), 'success')
end)

-- Seat switching
RegisterCommand('seat', function(source, args)
    if not isInVehicle then return end
    
    local seatIndex = tonumber(args[1])
    if not seatIndex then return end
    
    -- Convert seat number to game seat index
    local gameSeatIndex = seatIndex - 1
    if seatIndex == 1 then gameSeatIndex = -1 end -- Driver seat
    
    -- Check if seat exists
    if not IsVehicleSeatFree(currentVehicle, gameSeatIndex) then
        if GetPedInVehicleSeat(currentVehicle, gameSeatIndex) == PlayerPedId() then
            return -- Already in this seat
        end
        QBCore.Functions.Notify(locale('SEAT_OCCUPIED'), 'error')
        return
    end
    
    -- Check if vehicle has this seat
    if GetVehicleModelNumberOfSeats(GetEntityModel(currentVehicle)) < seatIndex then
        QBCore.Functions.Notify(locale('VEH_DOESNT_HAVE_THIS_SEAT'), 'error')
        return
    end
    
    -- Switch seat
    SetPedIntoVehicle(PlayerPedId(), currentVehicle, gameSeatIndex)
    isDriver = gameSeatIndex == -1
end)

-- Key mappings
RegisterKeyMapping('seatbelt', 'Toggle Seatbelt', 'keyboard', 'B')
RegisterKeyMapping('engine', 'Toggle Engine', 'keyboard', 'G')

-- Exports
exports('getSeatbeltStatus', function()
    return seatbeltOn
end)

exports('getVehicleHudActive', function()
    return vehicleHudActive
end)

exports('getCurrentVehicle', function()
    return currentVehicle
end)
