-- Valic HUD - Tickets Module
local QBCore = exports['qb-core']:GetCoreObject()

RegisterNetEvent('valic_hud:client:showTicket', function(ticketData)
    SendNUIMessage({
        action = 'showTicket',
        data = ticketData
    })
    SetNuiFocus(true, true)
end)

RegisterNetEvent('valic_hud:client:createTicket', function()
    local inputs = {
        {
            text = locale('NAME_LC'),
            name = 'name',
            type = 'text',
            isRequired = true
        },
        {
            text = locale('SID_LC'),
            name = 'sid',
            type = 'text',
            isRequired = true
        },
        {
            text = locale('VIOLATION_LC'),
            name = 'violation',
            type = 'text',
            isRequired = true
        },
        {
            text = locale('FINE_LC'),
            name = 'fine',
            type = 'number',
            isRequired = true
        }
    }

    exports['valic_hud-editos-fakos']:showMultiInput('Create Ticket', inputs, 'Create', 'Cancel')
end)

RegisterNUICallback('closeTicket', function(data, cb)
    SetNuiFocus(false, false)
    cb('ok')
end)