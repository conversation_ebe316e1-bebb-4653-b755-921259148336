-- ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗ ██████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
-- ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝██║██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
-- ██╔██╗ ██║██║   ██║   ██║   ██║█████╗  ██║██║     ███████║   ██║   ██║██║   ██║██╔██╗ ██║
-- ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝  ██║██║     ██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
-- ██║ ╚████║╚██████╔╝   ██║   ██║██║     ██║╚██████╗██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
-- ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝     ╚═╝ ╚═════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝
-- 
-- Valic HUD - Notification Module
-- Author: Valic
-- Description: Advanced notification system

local QBCore = exports['qb-core']:GetCoreObject()

-- Notification queue and settings
local notificationQueue = {}
local maxNotifications = 5
local defaultDuration = 5000

-- Notification types with styling
local notificationTypes = {
    ['success'] = {
        icon = 'fas fa-check-circle',
        color = '#10B981'
    },
    ['error'] = {
        icon = 'fas fa-times-circle',
        color = '#EF4444'
    },
    ['warning'] = {
        icon = 'fas fa-exclamation-triangle',
        color = '#F59E0B'
    },
    ['info'] = {
        icon = 'fas fa-info-circle',
        color = '#3B82F6'
    },
    ['primary'] = {
        icon = 'fas fa-bell',
        color = '#8B5CF6'
    }
}

-- Initialize notifications
RegisterNetEvent('valic_hud:client:setupNotifications', function()
    -- Setup is handled automatically
end)

-- Show notification function
function ShowNotification(message, type, duration, title, actions)
    type = type or 'info'
    duration = duration or defaultDuration
    
    local notification = {
        id = GetGameTimer() + math.random(1000, 9999),
        message = message,
        type = type,
        duration = duration,
        title = title,
        actions = actions,
        timestamp = GetGameTimer()
    }
    
    -- Add to queue
    table.insert(notificationQueue, notification)
    
    -- Remove oldest if queue is full
    if #notificationQueue > maxNotifications then
        table.remove(notificationQueue, 1)
    end
    
    -- Send to UI
    SendNUIMessage({
        action = 'showNotification',
        data = notification
    })
    
    return notification.id
end

-- Hide notification
function HideNotification(id)
    for i, notification in ipairs(notificationQueue) do
        if notification.id == id then
            table.remove(notificationQueue, i)
            break
        end
    end
    
    SendNUIMessage({
        action = 'hideNotification',
        data = { id = id }
    })
end

-- Clear all notifications
function ClearAllNotifications()
    notificationQueue = {}
    SendNUIMessage({
        action = 'clearAllNotifications'
    })
end

-- Advanced notification with custom styling
function ShowAdvancedNotification(data)
    local notification = {
        id = GetGameTimer() + math.random(1000, 9999),
        message = data.message,
        type = data.type or 'info',
        duration = data.duration or defaultDuration,
        title = data.title,
        subtitle = data.subtitle,
        icon = data.icon,
        image = data.image,
        actions = data.actions,
        position = data.position or 'top-right',
        sound = data.sound,
        timestamp = GetGameTimer()
    }
    
    -- Add to queue
    table.insert(notificationQueue, notification)
    
    -- Remove oldest if queue is full
    if #notificationQueue > maxNotifications then
        table.remove(notificationQueue, 1)
    end
    
    -- Play sound if specified
    if notification.sound then
        PlaySoundFrontend(-1, notification.sound.name, notification.sound.set, false)
    end
    
    -- Send to UI
    SendNUIMessage({
        action = 'showAdvancedNotification',
        data = notification
    })
    
    return notification.id
end

-- Events
RegisterNetEvent('valic_hud:client:notify', function(message, type, duration, title)
    ShowNotification(message, type, duration, title)
end)

RegisterNetEvent('valic_hud:client:advancedNotify', function(data)
    ShowAdvancedNotification(data)
end)

RegisterNetEvent('valic_hud:client:hideNotification', function(id)
    HideNotification(id)
end)

RegisterNetEvent('valic_hud:client:clearNotifications', function()
    ClearAllNotifications()
end)

-- NUI Callbacks
RegisterNUICallback('notificationAction', function(data, cb)
    -- Handle notification action clicks
    TriggerEvent('valic_hud:client:notificationAction', data.notificationId, data.action)
    cb('ok')
end)

RegisterNUICallback('dismissNotification', function(data, cb)
    HideNotification(data.id)
    cb('ok')
end)

-- QBCore notification override
local originalNotify = QBCore.Functions.Notify
QBCore.Functions.Notify = function(message, type, duration)
    ShowNotification(message, type, duration)
end

-- Police/EMS notifications
RegisterNetEvent('police:client:policeAlert', function(data)
    ShowAdvancedNotification({
        title = 'Police Alert',
        message = data.message,
        type = 'warning',
        duration = 8000,
        icon = 'fas fa-shield-alt',
        sound = {
            name = 'CONFIRM_BEEP',
            set = 'HUD_MINI_GAME_SOUNDSET'
        },
        actions = {
            {
                label = 'Respond',
                action = 'police:respond',
                data = data
            },
            {
                label = 'Dismiss',
                action = 'dismiss'
            }
        }
    })
end)

RegisterNetEvent('ambulance:client:emsAlert', function(data)
    ShowAdvancedNotification({
        title = 'EMS Alert',
        message = data.message,
        type = 'error',
        duration = 8000,
        icon = 'fas fa-ambulance',
        sound = {
            name = 'CONFIRM_BEEP',
            set = 'HUD_MINI_GAME_SOUNDSET'
        },
        actions = {
            {
                label = 'Respond',
                action = 'ems:respond',
                data = data
            },
            {
                label = 'Dismiss',
                action = 'dismiss'
            }
        }
    })
end)

-- Job notifications
RegisterNetEvent('valic_hud:client:jobNotification', function(job, message, type)
    local playerData = QBCore.Functions.GetPlayerData()
    if playerData.job and playerData.job.name == job then
        ShowNotification(message, type or 'info', 6000, string.upper(job))
    end
end)

-- Money change notifications
RegisterNetEvent('hud:client:OnMoneyChange', function(type, amount, reason)
    local message = ''
    local notifType = 'info'
    
    if amount > 0 then
        message = '+$' .. ValicHUD.Utils.FormatNumber(amount)
        notifType = 'success'
    else
        message = '-$' .. ValicHUD.Utils.FormatNumber(math.abs(amount))
        notifType = 'error'
    end
    
    if reason then
        message = message .. ' (' .. reason .. ')'
    end
    
    ShowNotification(message, notifType, 3000, string.upper(type))
end)

-- Exports
exports('notify', ShowNotification)
exports('advancedNotify', ShowAdvancedNotification)
exports('hideNotification', HideNotification)
exports('clearNotifications', ClearAllNotifications)
exports('getNotificationQueue', function()
    return notificationQueue
end)
