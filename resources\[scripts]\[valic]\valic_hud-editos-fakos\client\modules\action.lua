-- █████╗  ██████╗████████╗██╗ ██████╗ ███╗   ██╗
-- ██╔══██╗██╔════╝╚══██╔══╝██║██╔═══██╗████╗  ██║
-- ███████║██║        ██║   ██║██║   ██║██╔██╗ ██║
-- ██╔══██║██║        ██║   ██║██║   ██║██║╚██╗██║
-- ██║  ██║╚██████╗   ██║   ██║╚██████╔╝██║ ╚████║
-- ╚═╝  ╚═╝ ╚═════╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝
-- 
-- Valic HUD - Action Module
-- Author: Valic
-- Description: Action system for player interactions

local QBCore = exports['qb-core']:GetCoreObject()

-- Action state
local currentAction = nil
local actionCooldown = {}
local actionHistory = {}

-- Action types
local ACTION_TYPES = {
    PROGRESS = 'progress',
    ANIMATION = 'animation',
    SCENARIO = 'scenario',
    EMOTE = 'emote',
    CUSTOM = 'custom'
}

-- Initialize action system
CreateThread(function()
    while true do
        if currentAction then
            ProcessCurrentAction()
        end
        ProcessActionCooldowns()
        Wait(100)
    end
end)

-- Start action
function StartAction(actionData)
    if currentAction then
        QBCore.Functions.Notify(locale('ALREADY_DOING_ACTION'), 'error')
        return false
    end
    
    -- Check cooldown
    if actionCooldown[actionData.id] and actionCooldown[actionData.id] > GetGameTimer() then
        local remaining = math.ceil((actionCooldown[actionData.id] - GetGameTimer()) / 1000)
        QBCore.Functions.Notify('Action on cooldown for ' .. remaining .. ' seconds', 'error')
        return false
    end
    
    currentAction = {
        id = actionData.id,
        type = actionData.type or ACTION_TYPES.PROGRESS,
        label = actionData.label,
        duration = actionData.duration or 5000,
        canCancel = actionData.canCancel ~= false,
        useWhileDead = actionData.useWhileDead or false,
        disableControls = actionData.disableControls or {},
        animation = actionData.animation,
        prop = actionData.prop,
        propTwo = actionData.propTwo,
        scenario = actionData.scenario,
        onStart = actionData.onStart,
        onFinish = actionData.onFinish,
        onCancel = actionData.onCancel,
        startTime = GetGameTimer(),
        cooldown = actionData.cooldown or 0
    }
    
    -- Execute start callback
    if currentAction.onStart then
        currentAction.onStart()
    end
    
    -- Handle different action types
    if currentAction.type == ACTION_TYPES.PROGRESS then
        exports['valic_hud-editos-fakos']:showProgress({
            label = currentAction.label,
            duration = currentAction.duration,
            canCancel = currentAction.canCancel,
            useWhileDead = currentAction.useWhileDead,
            disableControls = currentAction.disableControls,
            animation = currentAction.animation,
            prop = currentAction.prop,
            propTwo = currentAction.propTwo,
            onFinish = function(success)
                FinishAction(success)
            end
        })
    elseif currentAction.type == ACTION_TYPES.ANIMATION then
        PlayActionAnimation()
    elseif currentAction.type == ACTION_TYPES.SCENARIO then
        PlayActionScenario()
    elseif currentAction.type == ACTION_TYPES.EMOTE then
        PlayActionEmote()
    end
    
    -- Add to history
    table.insert(actionHistory, {
        id = currentAction.id,
        timestamp = GetGameTimer(),
        duration = currentAction.duration
    })
    
    -- Keep only last 50 actions in history
    if #actionHistory > 50 then
        table.remove(actionHistory, 1)
    end
    
    return true
end

-- Process current action
function ProcessCurrentAction()
    if not currentAction then return end
    
    local elapsed = GetGameTimer() - currentAction.startTime
    
    -- Check for completion
    if elapsed >= currentAction.duration then
        FinishAction(true)
        return
    end
    
    -- Check for cancellation
    if currentAction.canCancel and IsControlJustPressed(0, 73) then -- X key
        CancelAction()
        return
    end
    
    -- Check if player died and action shouldn't continue
    if not currentAction.useWhileDead and IsEntityDead(PlayerPedId()) then
        CancelAction()
        return
    end
    
    -- Disable controls
    if currentAction.disableControls then
        for _, control in pairs(currentAction.disableControls) do
            DisableControlAction(0, control, true)
        end
    end
end

-- Finish action
function FinishAction(success)
    if not currentAction then return end
    
    local action = currentAction
    currentAction = nil
    
    -- Clean up animation/scenario
    CleanupAction(action)
    
    -- Set cooldown
    if action.cooldown > 0 then
        actionCooldown[action.id] = GetGameTimer() + (action.cooldown * 1000)
    end
    
    -- Execute finish callback
    if action.onFinish then
        action.onFinish(success)
    end
    
    TriggerEvent('valic_hud:client:actionFinished', action.id, success)
end

-- Cancel action
function CancelAction()
    if not currentAction then return end
    
    local action = currentAction
    currentAction = nil
    
    -- Clean up animation/scenario
    CleanupAction(action)
    
    -- Execute cancel callback
    if action.onCancel then
        action.onCancel()
    end
    
    TriggerEvent('valic_hud:client:actionCancelled', action.id)
    QBCore.Functions.Notify('Action cancelled', 'error')
end

-- Play animation
function PlayActionAnimation()
    if not currentAction.animation then return end
    
    local playerPed = PlayerPedId()
    local anim = currentAction.animation
    
    RequestAnimDict(anim.dict)
    while not HasAnimDictLoaded(anim.dict) do
        Wait(10)
    end
    
    TaskPlayAnim(
        playerPed,
        anim.dict,
        anim.anim,
        anim.blendIn or 8.0,
        anim.blendOut or 8.0,
        anim.duration or -1,
        anim.flags or 49,
        anim.playbackRate or 0,
        anim.lockX or false,
        anim.lockY or false,
        anim.lockZ or false
    )
end

-- Play scenario
function PlayActionScenario()
    if not currentAction.scenario then return end
    
    local playerPed = PlayerPedId()
    TaskStartScenarioInPlace(playerPed, currentAction.scenario, 0, true)
end

-- Play emote
function PlayActionEmote()
    if not currentAction.animation then return end
    
    -- This would integrate with an emote system
    TriggerEvent('animations:client:EmoteCommandStart', {currentAction.animation.emote})
end

-- Cleanup action
function CleanupAction(action)
    local playerPed = PlayerPedId()
    
    if action.animation then
        if action.animation.dict and action.animation.anim then
            StopAnimTask(playerPed, action.animation.dict, action.animation.anim, 1.0)
        elseif action.scenario then
            ClearPedTasksImmediately(playerPed)
        end
    end
    
    -- Clean up props would be handled by progress module
end

-- Process cooldowns
function ProcessActionCooldowns()
    local currentTime = GetGameTimer()
    for actionId, cooldownTime in pairs(actionCooldown) do
        if cooldownTime <= currentTime then
            actionCooldown[actionId] = nil
        end
    end
end

-- Predefined actions
local PREDEFINED_ACTIONS = {
    repair_vehicle = {
        id = 'repair_vehicle',
        label = 'Repairing Vehicle',
        duration = 15000,
        animation = {
            dict = 'mini@repair',
            anim = 'fixing_a_ped'
        },
        prop = {
            model = 'prop_tool_wrench',
            bone = 57005,
            pos = {x = 0.12, y = 0.15, z = 0.0},
            rot = {x = -60.0, y = 0.0, z = 0.0}
        },
        disableControls = {24, 25, 44, 45, 47, 58, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272},
        cooldown = 30
    },
    
    lockpick = {
        id = 'lockpick',
        label = 'Lockpicking',
        duration = 10000,
        animation = {
            dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
            anim = 'machinic_loop_mechandplayer'
        },
        disableControls = {24, 25, 44, 45, 47, 58},
        cooldown = 5
    },
    
    search = {
        id = 'search',
        label = 'Searching',
        duration = 8000,
        animation = {
            dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
            anim = 'machinic_loop_mechandplayer'
        },
        disableControls = {24, 25, 44, 45, 47, 58}
    }
}

-- Events
RegisterNetEvent('valic_hud:client:startAction', function(actionId, customData)
    local actionData = PREDEFINED_ACTIONS[actionId] or customData
    if actionData then
        StartAction(actionData)
    end
end)

RegisterNetEvent('valic_hud:client:cancelAction', function()
    CancelAction()
end)

-- Exports
exports('startAction', StartAction)
exports('cancelAction', CancelAction)
exports('isActionActive', function()
    return currentAction ~= nil
end)
exports('getCurrentAction', function()
    return currentAction
end)
exports('getActionHistory', function()
    return actionHistory
end)
exports('getPredefinedActions', function()
    return PREDEFINED_ACTIONS
end)
