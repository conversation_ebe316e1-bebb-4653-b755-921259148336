-- Test Commands for Valic HUD
-- Add this to client_scripts in fxmanifest.lua for testing

RegisterCommand('testhud', function()
    TriggerEvent('valic_hud:client:notify', 'HUD Test Message', 'success', 3000)
end)

RegisterCommand('testprogress', function()
    exports['valic_hud-editos-fakos']:showProgress({
        label = 'Testing Progress...',
        duration = 5000,
        canCancel = true
    })
end)

RegisterCommand('testinput', function()
    exports['valic_hud-editos-fakos']:showTextInput('Test Input', 'Enter something...', 50, true)
end)

RegisterCommand('testnotify', function()
    exports['valic_hud-editos-fakos']:advancedNotify({
        title = 'Test Notification',
        message = 'This is a test notification with actions',
        type = 'info',
        duration = 8000,
        actions = {
            {
                label = 'Accept',
                action = 'test_accept'
            },
            {
                label = 'Decline',
                action = 'test_decline'
            }
        }
    })
end)

RegisterCommand('testinteraction', function()
    exports['valic_hud-editos-fakos']:showInteractionMenu()
end)

RegisterCommand('testwelcome', function()
    TriggerEvent('valic_hud:client:showWelcome')
end)

print("^2[Valic HUD]^7 Test commands loaded: /testhud, /testprogress, /testinput, /testnotify, /testinteraction, /testwelcome")
