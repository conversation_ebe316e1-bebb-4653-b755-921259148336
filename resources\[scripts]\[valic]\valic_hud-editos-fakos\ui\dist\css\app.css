@import url(https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&display=swap);
@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap);
@import url(https://fonts.googleapis.com/css2?family=Caveat:wght@400..700&display=swap);
@import url(https://fonts.googleapis.com/css2?family=Permanent+Marker&display=swap);
@import url(https://fonts.googleapis.com/css2?family=Covered+By+Your+Grace&family=Merriweather:ital,opsz,wght@0,18..144,300..900;1,18..144,300..900&family=Source+Serif+4:ital,opsz,wght@0,8..60,200..900;1,8..60,200..900&display=swap);
:root {
    --perspective: 62.5rem;
    --green-active: #87da21;
    --bright-green: #9eff00;
    --green-active-rgb: 135, 218, 33;
    --modal-bg: rgba(26, 31, 20, 0.95);
    --input-bg: #0b1203;
}
@font-face {
    font-family: Geist;
    src: url(../fonts/Geist-Regular.woff2);
}
@font-face {
    font-family: Geist;
    src: url(../fonts/Geist-Bold.woff2);
    font-weight: 700;
}
@font-face {
    font-family: Geist;
    src: url(../fonts/Geist-Medium.woff2);
    font-weight: 401 699;
}
@font-face {
    font-family: Geist;
    src: url(../fonts/Geist-Light.woff2);
    font-weight: 0 399;
}
.grid {
    display: flex;
    gap: 1.5em;
    flex-wrap: wrap;
    justify-content: center;
}
calendar-month,
calendar-range {
    background: #0b1203;
    padding: 1rem;
    border-radius: 0.5rem;
    width: 100%;
}
calendar-date svg,
calendar-range svg {
    height: 1rem;
    width: 1rem;
    fill: none;
    stroke: #fff;
    stroke-width: 2;
}
calendar-date path,
calendar-range path {
    stroke-linecap: round;
    stroke-linejoin: round;
}
calendar-date::part(button),
calendar-range::part(button) {
    background-color: #0b1203;
    border-radius: 0.25rem;
    border: none;
    width: 26px;
    height: 26px;
}
calendar-date::part(button):focus-visible,
calendar-range::part(button):focus-visible {
    outline: none;
}
calendar-month {
    --color-accent: rgba(135, 218, 33, 0.5);
    --color-text-on-accent: #fff;
}
calendar-month::part(button) {
    border-radius: 0.325rem;
}
calendar-month::part(range-inner) {
    border-radius: 0;
    background-color: rgba(135, 218, 33, 0.25);
}
calendar-month::part(range-start) {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
}
calendar-month::part(range-end) {
    border-start-start-radius: 0;
    border-end-start-radius: 0;
}
calendar-month::part(range-start range-end) {
    border-radius: 0.25rem;
}
.vc-chrome {
    width: 100% !important;
    background: none !important;
    border-radius: 0.5rem;
}
.vc-chrome .vc-chrome-fields-wrap {
    display: none;
}
.vc-chrome .vc-chrome-body {
    background: hsla(0, 0%, 100%, 0.01);
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}
.vc-chrome .vc-chrome-saturation-wrap {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}
* {
    box-sizing: border-box;
}
::-webkit-scrollbar {
    width: 0.25rem;
}
::-webkit-scrollbar-track {
    background: #101010;
    border-radius: var(--XXS, 0.25rem);
}
::-webkit-scrollbar-thumb {
    border-radius: var(--XXS, 0.5rem);
    background: var(--green-active, #87da21);
}
h1,
h2,
h3,
h4,
h5,
p {
    margin: 0;
}
.error {
    color: red;
}
:root {
    font-size: min(0.83333333vw, 1.48148148vh);
    font-family: Oswald, sans-serif;
}
body,
html {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
#app,
.app,
body,
html {
    width: 100%;
    height: 100%;
}
#app,
.app {
    background: transparent;
}
.bold {
    font-weight: 700;
}
.app > .wrapper {
    position: fixed;
    pointer-events: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
.app > .wrapper > * {
    pointer-events: auto;
}
.loader {
    position: relative;
    isolation: isolate;
    overflow: hidden;
}
.loader-text {
    width: 80%;
    margin: 0 auto;
    height: 2em;
    border-radius: 1rem;
}
.loader:after {
    z-index: -2;
    background: hsla(0, 0%, 100%, 0.01);
}
.loader:after,
.loader:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.loader:before {
    z-index: -1;
    background: linear-gradient(
        90deg,
        transparent 8%,
        hsla(0, 0%, 100%, 0.05) 18%,
        transparent 33%
    );
    background-size: 100% 100%;
    animation: anim 1s linear -0.25s infinite;
    translate: 50% 0;
}
.accent-border {
    position: relative;
}
.accent-border:before {
    position: absolute;
    top: calc(var(--accentWidth) * -1 / 2);
    left: calc(var(--accentWidth) * -1 / 2);
    width: calc(100% - var(--accentWidth));
    height: calc(100% - var(--accentWidth));
    border: var(--accentWidth) solid var(--accentColor);
    border-radius: inherit;
    content: "";
    clip-path: polygon(
        0 0,
        var(--accent) 0,
        var(--accent) var(--accent),
        calc(100% - var(--accent)) var(--accent),
        100% var(--accent),
        100% 0,
        calc(100% - var(--accent)) 0,
        calc(100% - var(--accent)) calc(100% - var(--accent)),
        var(--accent) calc(100% - var(--accent)),
        var(--accent) 101%,
        0 100%,
        0 calc(100% - var(--accent)),
        var(--accent) calc(100% - var(--accent)),
        100% calc(100% - var(--accent)),
        100% 100%,
        calc(100% - var(--accent)) 100%,
        calc(100% - var(--accent)) calc(100% - var(--accent)),
        calc(100% - var(--accent)) var(--accent),
        50% var(--accent),
        0 var(--accent)
    );
}
.image-border {
    position: relative;
    isolation: isolate;
}
.image-border:before {
    position: absolute;
    top: calc(var(--imBorderWidth) * -1);
    left: calc(var(--imBorderWidth) * -1);
    width: 100%;
    height: 100%;
    padding: var(--imBorderWidth);
    box-sizing: content-box;
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    background: var(--imBorderBg);
    z-index: -1;
    transition: opacity 0.2s ease, background 0.2s ease;
    border-radius: inherit;
    content: "";
}
@keyframes anim {
    0% {
        left: -75%;
    }
    to {
        left: 75%;
    }
}
.fade-enter-active,
.fade-leave-active,
.fade-move,
.rotate-bottom-enter-active,
.rotate-bottom-leave-active,
.rotate-bottom-move,
.rotate-top-enter-active,
.rotate-top-leave-active,
.rotate-top-move,
.slide-left-enter-active,
.slide-left-leave-active,
.slide-left-move,
.slide-right-enter-active,
.slide-right-leave-active,
.slide-right-move {
    transition: all 0.3s ease-out;
}
.slide-left-enter-from,
.slide-left-leave-to {
    transform: translateX(-100%) rotateY(-20deg);
    opacity: 0;
}
.slide-right-enter-from,
.slide-right-leave-to {
    transform: translateX(100%) rotateY(20deg) !important;
    opacity: 0;
}
.rotate-top-enter-from,
.rotate-top-leave-to {
    transform: translateY(-100%) rotateX(20deg);
    opacity: 0;
}
.rotate-bottom-enter-from,
.rotate-bottom-leave-to {
    transform: translateY(100%) rotateX(-20deg);
    opacity: 0;
}
.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
.separated-progress-wrapper[data-v-6d1bfab8] {
    width: 100%;
    height: 0.5625rem;
    display: grid;
    grid-gap: 0.25rem;
    grid-template-columns: repeat(var(--parts), 1fr);
}
.separated-progress-wrapper .part[data-v-6d1bfab8] {
    position: relative;
    border-radius: 0.125rem;
    border: 0.25px solid var(--White-White-25, hsla(0, 0%, 100%, 0.1));
    background: var(--Black-Black-50, hsla(0, 0%, 7%, 0.6));
}
.separated-progress-wrapper .part .inner-part[data-v-6d1bfab8] {
    width: 100%;
    height: 100%;
    background: rgb(var(--bg));
    border-radius: inherit;
    scale: var(--val) 1;
    transform-origin: left center;
    transition: background 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 4px 4px 0 rgba(var(--bg), 0.25),
        0 4px 16px 0 rgba(var(--bg), 0.35);
}
.separated-progress-wrapper .part .inner-part.over[data-v-6d1bfab8] {
    position: absolute;
    top: 0;
}
.vertical-progress-wrapper[data-v-a9c519dc] {
    width: 0.1875rem;
    height: 1.5rem;
    border-radius: var(--border-radius-rounded-sm, 0.125rem);
    background: rgba(48, 48, 48, 0.95);
}
.vertical-progress-wrapper .internal[data-v-a9c519dc] {
    width: 100%;
    height: 100%;
    transform-origin: bottom center;
    scale: 1 var(--val);
    background: rgb(var(--bg));
    border-radius: inherit;
    box-shadow: 0 4px 4px 0 rgba(var(--bg), 0.25),
        0 4px 16px 0 rgba(var(--bg), 0.35);
    transition: scale 0.5s ease;
}
.undress-menu[data-v-9c2171fa] {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    color: #fff;
    font-weight: 400;
    font-size: 2rem;
    --green-bg: #425c2c;
    --dark-green-bg: rgba(26, 31, 20, 0.85);
    --black: rgba(0, 0, 0, 0.5);
    --black-hover: rgba(0, 0, 0, 0.75);
}
.undress-menu .buttons[data-v-9c2171fa] {
    width: -moz-fit-content;
    width: fit-content;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-gap: 1rem;
}
.undress-menu.pink[data-v-9c2171fa] {
    --green-active: #e840a6;
    --bright-green: #fc70c5;
    --green-active-rgb: 232, 64, 166;
    --green-bg: #442a36;
    --dark-green-bg: linear-gradient(
            0deg,
            rgba(255, 140, 196, 0.3),
            rgba(255, 140, 196, 0.3)
        ),
        rgba(0, 0, 0, 0.85);
    --button-bg: rgba(252, 112, 197, 0.8);
    --button-bg-hover: rgba(252, 112, 197, 0.4);
    --black: rgba(0, 0, 0, 0.5);
    --black-hover: rgba(0, 0, 0, 0.75);
}
.clothing-menu-category[data-v-9c2171fa] {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    border: 1px solid transparent;
    background: var(--dark-green-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.1s ease;
}
.clothing-menu-category svg[data-v-9c2171fa] {
    font-size: 1.5rem;
}
.clothing-menu-category.active[data-v-9c2171fa] {
    border-radius: 0.5rem;
    border: 1px solid var(--bright-green);
    background: var(--dark-green-bg);
}
.wrapper[data-v-9144415c] {
    position: absolute;
    bottom: 2.5rem;
    left: 2.5rem;
    transform-origin: left center;
    transform: rotateY(8deg);
    display: flex;
    flex-direction: column;
    gap: 2rem;
}
.hud[data-v-9144415c] {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}
.hud .col[data-v-9144415c] {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 0.25rem;
    height: 3.375rem;
}
.hud .col .element[data-v-9144415c] {
    display: flex;
    gap: 0.6125rem;
    align-items: center;
    font-family: Geist;
    font-size: 0.725rem;
    font-style: normal;
    font-weight: 500;
    position: relative;
}
.hud .col .element .data[data-v-9144415c] {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}
.hud .col .element .data p[data-v-9144415c] {
    width: 1rem;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}
.hud .col .element .data svg[data-v-9144415c] {
    height: 1rem;
    fill: hsla(0, 0%, 100%, 0.5);
    stroke: hsla(0, 0%, 100%, 0.5);
}
.hud .col .element .data svg.dying[data-v-9144415c] {
    fill: rgba(255, 0, 0, 0.5);
    stroke: red;
    animation: dying-9144415c 2s infinite !important;
}
.hud .col .element .data svg.incHealing[data-v-9144415c],
.hud .col .element .data svg.noDmg[data-v-9144415c] {
    fill: var(--color);
    stroke: var(--color);
}
.hud .col .element .data svg.blinkHeart[data-v-9144415c] {
    animation: blink-heart-9144415c 1s infinite;
}
.hud .col .element .data svg.noDmg[data-v-9144415c] {
    --color: rgba(255, 174, 0, 0.5);
}
.hud .col .element .data svg.incHealing[data-v-9144415c] {
    --color: #87da21;
}
.hud .col .element .prog[data-v-9144415c] {
    width: 13.25rem;
    transition: box-shadow 0.2s ease;
}
.hud .col .element .dyingShadow[data-v-9144415c] {
    animation: dying-shadow-9144415c 2s infinite !important;
}
.hud .col .element .hit[data-v-9144415c] {
    box-shadow: 0 0 8px 0 rgba(255, 0, 0, 0.9), 0 0 16px 0 rgba(255, 0, 0, 0.65);
}
.hud .elements[data-v-9144415c] {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
.hud .elements .element[data-v-9144415c] {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}
.hud .elements .element svg[data-v-9144415c] {
    height: 1.25rem;
    transition: fill 0.2s ease, stroke 0.2s ease;
}
@keyframes dying-shadow-9144415c {
    0% {
        box-shadow: 0 0 8px 0 rgba(255, 0, 0, 0.25),
            0 0 16px 0 rgba(255, 0, 0, 0.35);
    }
    50% {
        box-shadow: 0 0 8px 0 rgba(255, 0, 0, 0.5),
            0 0 16px 0 rgba(255, 0, 0, 0.65);
    }
    to {
        box-shadow: 0 0 8px 0 rgba(255, 0, 0, 0.25),
            0 0 16px 0 rgba(255, 0, 0, 0.35);
    }
}
@keyframes dying-9144415c {
    0% {
        fill: rgba(255, 0, 0, 0.5);
        stroke: red;
    }
    50% {
        scale: 1.25;
        fill: red;
        stroke: red;
    }
    to {
        fill: rgba(255, 0, 0, 0.5);
        stroke: red;
    }
}
.status-list-enter-active[data-v-9144415c],
.status-list-leave-active[data-v-9144415c],
.status-list-move[data-v-9144415c] {
    transform-origin: bottom center;
    transition: all 0.5s ease;
}
.status-list-enter-from[data-v-9144415c],
.status-list-leave-to[data-v-9144415c] {
    opacity: 0;
    transform: rotateX(90deg) translateY(100%);
}
.status-list-leave-active[data-v-9144415c] {
    position: absolute;
}
.bar-list-enter-active[data-v-9144415c],
.bar-list-leave-active[data-v-9144415c],
.bar-list-move[data-v-9144415c] {
    transform-origin: left center;
    transition: all 0.5s ease;
}
.bar-list-enter-from[data-v-9144415c],
.bar-list-leave-to[data-v-9144415c] {
    opacity: 0;
    transform: translateX(-100%) translateY(-50%);
}
.bar-list-leave-active[data-v-9144415c] {
    position: absolute;
}
.blink[data-v-9144415c] {
    animation: blink-9144415c 1s infinite;
}
@keyframes blink-9144415c {
    0%,
    to {
        stroke: var(--internal-color);
    }
    50% {
        stroke: transparent;
    }
}
@keyframes blink-heart-9144415c {
    0%,
    to {
        stroke: var(--color);
        fill: var(--color);
    }
    50% {
        stroke: transparent;
        fill: transparent;
    }
}
.voice-bar[data-v-3117a3ac] {
    width: 3px;
    height: var(--height);
    border-radius: 2px;
    background: #545454;
    transition: height 0.1s ease, background 0.2s ease, box-shadow 0.2s ease;
}
.voice-bar.fake[data-v-3117a3ac] {
    transition: height 0.2s ease, background 0.2s ease, box-shadow 0.2s ease;
}
.voice-bar.active[data-v-3117a3ac] {
    background: #87da21;
    box-shadow: 0 4px 4px 0 rgba(135, 218, 33, 0.25),
        0 4px 16px 0 rgba(135, 218, 33, 0.35);
}
.voice-bar.active.runwayMode[data-v-3117a3ac] {
    background: #fc70c5;
    box-shadow: 0 4px 4px 0 rgba(252, 112, 197, 0.25),
        0 4px 16px 0 rgba(252, 112, 197, 0.35);
}
.voice-bar.radio[data-v-3117a3ac] {
    background: #00b2ff;
    box-shadow: 0 4px 4px 0 rgba(0, 178, 255, 0.25),
        0 4px 16px 0 rgba(0, 178, 255, 0.35);
}
.voice-wrapper[data-v-82b8c6be] {
    display: flex;
    flex-direction: column;
    gap: 0.6875rem;
    width: 2.125rem;
    position: relative;
}
.voice-wrapper .indicator[data-v-82b8c6be] {
    position: absolute;
    top: 0;
    left: 50%;
    translate: -50% calc(-100% - 0.5rem);
}
.voice-wrapper .indicator svg[data-v-82b8c6be] {
    height: 1.5rem;
    color: #87da21;
}
.voice-wrapper.runwayMode .indicator svg[data-v-82b8c6be] {
    color: #fc70c5;
}
.voice-wrapper .voice-bars[data-v-82b8c6be] {
    display: flex;
    grid-template-columns: repeat(var(--c), 1fr);
    justify-content: space-between;
    align-items: center;
    height: 2rem;
}
.voice-wrapper .voice-prog[data-v-82b8c6be] {
    grid-gap: 0.125rem;
}
.voice-wrapper .voice-prog[data-v-82b8c6be] .part {
    border-radius: var(--border-radius-rounded-sm, 0.125rem);
    border: 0.25px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: var(--Black-Black-50, hsla(0, 0%, 7%, 0.5));
    height: 0.3125rem;
}
.voice-wrapper .dots[data-v-82b8c6be] {
    display: grid;
    grid-gap: 0.125rem;
    grid-template-columns: 1fr 1fr 1fr;
    justify-content: center;
    height: 0.25rem;
}
.voice-wrapper .dots .dot[data-v-82b8c6be] {
    height: 100%;
    border-radius: var(--border-radius-rounded-sm, 0.125rem);
    border: 0.25px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: var(--Black-Black-50, hsla(0, 0%, 7%, 0.5));
}
.voice-wrapper .dots .dot.active[data-v-82b8c6be] {
    border-radius: 0.15344rem;
    background: var(--Brand-Brand, #87da21);
}
.ammo-wrapper[data-v-b4195c52] {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.ammo-wrapper img[data-v-b4195c52] {
    width: 2rem;
    height: 2rem;
}
.ammo-wrapper .col[data-v-b4195c52] {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 2rem;
}
.ammo-wrapper .col .large[data-v-b4195c52] {
    color: var(--Brand-Brand, #87da21);
    text-shadow: 0 4.909px 4.909px rgba(135, 218, 33, 0.25),
        0 4.909px 19.637px rgba(135, 218, 33, 0.35);
    font-family: Geist;
    font-size: 2rem;
    font-style: normal;
    font-weight: 700;
    transition: text-shadow 0.2s ease, color 0.2s ease;
}
.ammo-wrapper .col .large.gray[data-v-b4195c52] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    text-shadow: none;
}
.ammo-wrapper .col .sm[data-v-b4195c52] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-family: Geist;
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
}
.horizontal-progress-wrapper[data-v-44193ac0] {
    height: 0.1875rem;
    width: 1.5rem;
    border-radius: var(--border-radius-rounded-sm, 0.125rem);
    background: rgba(48, 48, 48, 0.95);
}
.horizontal-progress-wrapper .internal[data-v-44193ac0] {
    width: 100%;
    height: 100%;
    transform-origin: left center;
    scale: var(--val) 1;
    background: rgb(var(--bg));
    border-radius: inherit;
    box-shadow: 0 4px 4px 0 rgba(var(--bg), 0.25),
        0 4px 16px 0 rgba(var(--bg), 0.35);
    transition: scale 0.5s ease;
}
.car-hud[data-v-3fcf9739] {
    width: 17.375rem;
    height: 17.4375rem;
    color: #fff;
    position: relative;
    top: 2rem;
    margin: 0 -1rem;
}
.car-hud canvas[data-v-3fcf9739] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.car-hud .center[data-v-3fcf9739] {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}
.car-hud .center svg[data-v-3fcf9739] {
    width: 1.5rem;
    height: 1.5rem;
}
.car-hud .center h3[data-v-3fcf9739] {
    color: var(--white-100, #fff);
    font-family: Oswald;
    font-size: 3.5rem;
    font-weight: 400;
    line-height: 100%;
    width: 5rem;
    text-align: center;
}
.car-hud .center h4[data-v-3fcf9739] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Oswald;
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 100%;
}
.car-hud .controls[data-v-3fcf9739] {
    position: absolute;
    bottom: 1.875rem;
    left: 50%;
    translate: -50% 0;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}
.car-hud .controls .control[data-v-3fcf9739] {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0;
}
.rotate-bottom-leave-active[data-v-3fcf9739] {
    position: absolute;
}
.nitro-icons[data-v-3fcf9739] {
    width: 100%;
    display: flex;
    justify-content: space-between;
    position: absolute;
    bottom: 18%;
    translate: 0 -50%;
    padding: 0 1.5rem;
}
.nitro-icons svg[data-v-3fcf9739] {
    stroke-width: 2px;
    stroke: #87da21;
}
.hud-right[data-v-e147c9aa] {
    position: absolute;
    bottom: 2.5rem;
    right: 2.5rem;
    height: 5rem;
    transform-origin: right center;
    transform: rotateY(-8deg);
    display: flex;
    flex-direction: row-reverse;
    align-items: flex-end;
    gap: 1.5rem;
    margin-bottom: var(--phoneHeight);
    transition: margin-bottom 0.2s linear;
}
.rotate-in-enter-active[data-v-e147c9aa],
.rotate-in-leave-active[data-v-e147c9aa],
.rotate-in-move[data-v-e147c9aa] {
    transform-origin: bottom center;
    transition: all 0.5s ease;
}
.rotate-in-enter-from[data-v-e147c9aa],
.rotate-in-leave-to[data-v-e147c9aa] {
    opacity: 0;
    transform: rotateX(90deg) translateY(100%);
}
.rotate-in-leave-active[data-v-e147c9aa] {
    position: absolute;
}
.runway-timer[data-v-58b889fc] {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 10rem;
    padding: var(--gap-gap-4, 0.5rem) var(--gap-gap-6, 1.5rem);
    border-radius: 0.5rem;
    border: 1px solid transparent;
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0.25) 2.22%,
            rgba(252, 112, 197, 0.55)
        ),
        rgba(0, 0, 0, 0.75);
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(-45deg, transparent, #fc70c5);
    color: #fc70c5;
    text-shadow: 0 4px 24px rgba(252, 112, 197, 0.5);
    font-family: Oswald;
    font-size: 2rem;
}
.runway-timer.red[data-v-58b889fc] {
    background: linear-gradient(
            143deg,
            rgba(26, 9, 9, 0.25) 2.22%,
            rgba(252, 12, 12, 0.55)
        ),
        rgba(0, 0, 0, 0.75);
    --imBorderBg: linear-gradient(-45deg, transparent, rgba(252, 12, 12, 0.85));
    --box-shadow: 252, 12, 12;
    animation: pulse-shadow-58b889fc 3s ease-in-out infinite;
    color: #fc0c0c;
}
.runway-timer .dark[data-v-58b889fc] {
    color: var(--White-80, hsla(0, 0%, 100%, 0.8));
    font-family: Oswald;
    font-size: 1rem;
    font-weight: 400;
    text-shadow: none;
}
@keyframes pulse-shadow-58b889fc {
    0% {
        box-shadow: 0 4px 24px 0 rgba(var(--box-shadow), 0.15);
    }
    50% {
        box-shadow: 0 4px 24px 0 rgba(var(--box-shadow), 0.45);
    }
    to {
        box-shadow: 0 4px 24px 0 rgba(var(--box-shadow), 0.15);
    }
}
.runway-top-text[data-v-14f7f8fe] {
    color: #fff;
    text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    align-items: center;
}
.runway-top-text h3[data-v-14f7f8fe] {
    font-size: 3rem;
    font-style: normal;
    font-weight: 400;
}
.runway-top-text h4[data-v-14f7f8fe] {
    font-size: 2rem;
    font-style: normal;
    font-weight: 400;
}
.heist-timer[data-v-81f7b4ce] {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    font-family: Oswald;
    font-size: 1.25rem;
    font-weight: 400;
    text-align: center;
}
.heist-timer span[data-v-81f7b4ce] {
    font-size: 2.25rem;
    position: relative;
    color: #fff;
}
.heist-timer .delta[data-v-81f7b4ce] {
    position: absolute;
    right: -1rem;
    font-size: 1.25rem;
    top: 50%;
    translate: 100% -50%;
    color: #9aff00;
    leading-trim: both;
    text-edge: cap;
    text-shadow: 0 4px 24px rgba(153, 255, 0, 0.5);
}
.heist-timer .delta.negative[data-v-81f7b4ce] {
    color: #ff0020;
    text-shadow: 0 4px 24px rgba(255, 0, 32, 0.5);
}
.heist-timer.med[data-v-81f7b4ce] {
    color: #a18a00;
}
.heist-timer.med span[data-v-81f7b4ce] {
    color: #ffe000;
    text-shadow: 0 4px 24px rgba(255, 224, 0, 0.5);
}
.heist-timer.low[data-v-81f7b4ce] {
    color: #a00000;
}
.heist-timer.low span[data-v-81f7b4ce] {
    color: #ff0020;
    text-shadow: 0 4px 24px rgba(255, 0, 32, 0.5);
}
.top-info-wrapper[data-v-93f8469a] {
    position: absolute;
    top: 2.5rem;
    left: 50%;
    translate: -50% 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}
.top-info[data-v-93f8469a] {
    display: flex;
    gap: 0.75rem;
    transform-origin: center top;
    perspective: 10rem;
}
.top-info[data-v-93f8469a] svg {
    width: 1.5rem;
}
.top-info .section[data-v-93f8469a] {
    transform: rotateX(-2deg);
}
.crazy-taxi[data-v-93f8469a] {
    padding: var(--gap-gap-2, 0.5rem) 1rem;
    border-radius: 0.75rem;
    background: linear-gradient(
            90deg,
            rgba(237, 0, 0, 0.85),
            rgba(255, 172, 47, 0.85)
        ),
        rgba(0, 0, 0, 0.5);
    box-shadow: 8px 8px 24px 0 rgba(255, 255, 0, 0.25),
        -4px 4px 8px 0 rgba(237, 0, 0, 0.5);
    font-family: Geist;
    font-size: 1.125rem;
    font-weight: 500;
    color: #fff;
}
.section[data-v-7bd8613c] {
    position: absolute;
    transform: rotateY(8deg);
}
.section svg[data-v-7bd8613c] {
    height: 1rem;
    width: 1rem;
}
.bar[data-v-16e60340] {
    width: 100%;
    height: 0.55894rem;
    position: relative;
    isolation: isolate;
}
.bar .background[data-v-16e60340] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 1.5rem;
    overflow: hidden;
}
.bar .background img[data-v-16e60340] {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.bar .bar-inner[data-v-16e60340] {
    width: var(--scale);
    height: 100%;
    background: #87da21;
    border-radius: 1.5rem calc(var(--progress) * 1.5rem)
        calc(var(--progress) * 1.5rem) 1.5rem;
    box-shadow: 0 4px 16px 0 rgba(158, 255, 0, 0.5),
        0 4px 24px 0 rgba(158, 255, 0, 0.75);
}
.bar .bar-inner.canceled[data-v-16e60340] {
    background: red;
    box-shadow: 0 4px 16px 0 rgba(255, 0, 0, 0.5),
        0 4px 24px 0 rgba(255, 0, 0, 0.75);
}
.bar .bar-inner.smallGlow[data-v-16e60340] {
    box-shadow: 0 2px 8px 0 rgba(158, 255, 0, 0.25),
        0 2px 12px 0 rgba(158, 255, 0, 0.5);
}
.bar .bar-inner.smallGlow.canceled[data-v-16e60340] {
    box-shadow: 0 2px 8px 0 rgba(255, 0, 0, 0.25),
        0 2px 12px 0 rgba(255, 0, 0, 0.5);
}
.progbar-wrapper[data-v-67e5584b] {
    width: 20rem;
    color: #fff;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.progbar-wrapper .row[data-v-67e5584b] {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1.125rem;
}
.progbar-wrapper .row span[data-v-67e5584b] {
    color: #87da21;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 500;
}
.progbar-wrapper .row span.canceled[data-v-67e5584b] {
    color: red;
}
.action[data-v-7b7846c9] {
    color: #fff;
    font-family: Oswald;
    font-size: 1.125rem;
    text-transform: uppercase;
}
.action .key[data-v-7b7846c9] {
    height: 2rem;
    min-width: 2rem;
    display: inline-block;
    text-align: center;
    padding: 0 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    background: hsla(0, 0%, 100%, 0.15);
    line-height: 170%;
    vertical-align: center;
    margin: 0 0.25rem;
}
.runway-rating[data-v-60c97146] {
    position: relative;
    border-radius: 0.5rem;
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0) 2.22%,
            rgba(252, 112, 197, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    padding: var(--gap-gap-4, 1rem) var(--gap-gap-6, 1.5rem);
    padding-bottom: 8rem;
    color: var(--White-80, hsla(0, 0%, 100%, 0.8));
    font-family: Oswald;
    font-size: 1.25rem;
    width: 18rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(135deg, transparent, #fc70c5);
}
.runway-rating button[data-v-60c97146] {
    position: absolute;
    bottom: 1rem;
    display: flex;
    padding: 0.75rem 1rem;
    height: -moz-fit-content;
    height: fit-content;
    align-items: center;
    justify-content: center;
    width: -moz-fit-content;
    width: fit-content;
    gap: 0.6125rem;
    padding: 1rem 1.5rem;
    border-radius: 0.375rem;
    color: var(--White, #fff);
    font-family: Oswald;
    font-size: 1rem;
    cursor: pointer;
    line-height: 1;
    border: 1px solid transparent;
    background: transparent;
    transition: border 0.2s ease;
}
.runway-rating button[data-v-60c97146]:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    background: linear-gradient(
        90deg,
        rgba(252, 112, 197, 0.8),
        rgba(232, 64, 166, 0.8)
    );
    --imBorderBg: linear-gradient(-45deg, hsla(0, 0%, 100%, 0.1), #fc70c5);
    z-index: -1;
    transition: opacity 0.2s ease;
}
.runway-rating button[data-v-60c97146]:hover {
    border: 1px solid #fc70c5;
}
.runway-rating button[data-v-60c97146]:hover:after,
.runway-rating button[data-v-60c97146]:hover:before {
    opacity: 0;
}
.runway-rating canvas[data-v-60c97146] {
    position: absolute;
    top: 9.5rem;
    left: 50%;
    translate: -50% -100%;
}
.runway-flare[data-v-e0ded02e] {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    width: -moz-fit-content;
    width: fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border: 1px solid #e0e0e0;
    background: linear-gradient(
            0deg,
            hsla(0, 0%, 88%, 0.15),
            hsla(0, 0%, 88%, 0.15)
        ),
        rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 8px 0 hsla(0, 0%, 88%, 0.25);
    transition: all 0.2s ease;
    --imBorderBg: transparent;
    --imBorderWidth: 1px;
    color: #fff;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.25rem;
    flex-shrink: 0;
}
.runway-flare.big[data-v-e0ded02e] {
    font-family: Oswald;
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}
.runway-flare svg[data-v-e0ded02e] {
    margin-bottom: 0.1rem;
}
.runway-flare.dark[data-v-e0ded02e] {
    box-shadow: none;
    background: #141414;
    border: 1px solid transparent;
}
.runway-flare.D[data-v-e0ded02e] {
    border: 1px solid #ff00f5;
    background: linear-gradient(
            0deg,
            rgba(255, 0, 245, 0.15),
            rgba(255, 0, 245, 0.15)
        ),
        rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 8px 0 rgba(158, 0, 255, 0.25);
}
.runway-flare.C[data-v-e0ded02e] {
    border: 1px solid #9e00ff;
    background: linear-gradient(
            0deg,
            rgba(158, 0, 255, 0.15),
            rgba(158, 0, 255, 0.15)
        ),
        rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 8px 0 rgba(158, 0, 255, 0.25);
}
.runway-flare.B[data-v-e0ded02e] {
    border: 1px solid #00c2ff;
    background: linear-gradient(
            0deg,
            rgba(0, 194, 255, 0.15),
            rgba(0, 194, 255, 0.15)
        ),
        rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 8px 0 rgba(0, 194, 255, 0.25);
}
.runway-flare.A[data-v-e0ded02e] {
    border: 1px solid #ffc700;
    background: linear-gradient(
            0deg,
            rgba(255, 199, 0, 0.15),
            rgba(255, 199, 0, 0.15)
        ),
        rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 8px 0 rgba(255, 199, 0, 0.25);
}
.runway-flare.S[data-v-e0ded02e],
.runway-flare.green[data-v-e0ded02e] {
    border: 1px solid #87da21;
    background: linear-gradient(
            0deg,
            rgba(135, 218, 33, 0.15),
            rgba(135, 218, 33, 0.15)
        ),
        rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 8px 0 rgba(135, 218, 33, 0.25);
}
.runway-flare.red[data-v-e0ded02e] {
    border: 1px solid var(--Red-Red, red);
    background: linear-gradient(
            0deg,
            var(--Red-Red-15, rgba(255, 0, 0, 0.15)) 0,
            var(--Red-Red-15, rgba(255, 0, 0, 0.15)) 100%
        ),
        rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 8px 0 rgba(255, 0, 0, 0.25);
}
.runway-flare.rainbow-green[data-v-e0ded02e] {
    border: 1px solid #87da21;
    background: linear-gradient(
            98deg,
            rgba(135, 218, 33, 0.1) 2.7%,
            rgba(207, 186, 0, 0.1) 97.61%
        ),
        #0a0a0a;
    --imBorderBg: linear-gradient(98deg, #87da21 2.7%, #cfba00 97.61%);
    --imBorderWidth: 1px;
}
.runway-flare.rainbow-red[data-v-e0ded02e] {
    border: 1px solid #ff0e0e;
    background: linear-gradient(
            98deg,
            rgba(255, 14, 14, 0.1) 2.7%,
            rgba(207, 186, 0, 0.1) 97.61%
        ),
        linear-gradient(
            98deg,
            rgba(255, 14, 14, 0.1) 2.7%,
            rgba(207, 186, 0, 0.1) 97.61%
        );
    box-shadow: 0 4px 24px 0 rgba(207, 186, 0, 0.25);
    --imBorderBg: linear-gradient(120.95deg, #ff0e0e 2.11%, #cfba00);
    --imBorderWidth: 1px;
}
.runway-flare.pos[data-v-e0ded02e] {
    border-radius: 0.5rem;
    border: 1px solid transparent;
    background: var(--White-White-10, hsla(0, 0%, 100%, 0.1));
    box-shadow: none;
    --imBorderBg: linear-gradient(
        120.95deg,
        #ccc 2.11%,
        hsla(0, 0%, 80%, 0) 51.54%,
        #ccc
    );
    --imBorderWidth: 1px;
}
.runway-flare.pink[data-v-e0ded02e] {
    background: #401d32;
    border: 1px solid transparent;
    --imBorderBg: linear-gradient(
        120.95deg,
        #fc70c5 2.11%,
        rgba(252, 112, 197, 0) 51.54%,
        #fc70c5
    );
    --box-shadow: 252, 112, 197;
    animation: pulse-shadow-e0ded02e 3s ease-in-out infinite;
}
.runway-flare.pos-1[data-v-e0ded02e] {
    background: rgba(255, 199, 0, 0.1);
    --imBorderBg: linear-gradient(
        120.95deg,
        #ffc700 2.11%,
        rgba(255, 199, 0, 0) 51.54%,
        #ffc700
    );
    --box-shadow: 255, 199, 0;
    animation: pulse-shadow-e0ded02e 3s ease-in-out infinite;
}
.runway-flare.pos-2[data-v-e0ded02e] {
    background: var(--White-White-10, hsla(0, 0%, 100%, 0.1));
    --box-shadow: 255, 255, 255;
    --imBorderBg: linear-gradient(
        120.95deg,
        #fff 2.11%,
        hsla(0, 0%, 100%, 0) 51.54%,
        #fff
    );
    animation: pulse-shadow-e0ded02e 3s ease-in-out infinite;
}
.runway-flare.pos-3[data-v-e0ded02e] {
    --imBorderBg: linear-gradient(
        120.95deg,
        #fe6712 2.11%,
        rgba(254, 103, 18, 0) 51.54%,
        #fe6712
    );
    background: rgba(254, 103, 18, 0.1);
    --box-shadow: 254, 103, 18;
    animation: pulse-shadow-e0ded02e 3s ease-in-out infinite;
}
.runway-flare.elo[data-v-e0ded02e] {
    border: 1px solid transparent;
    --imBorderBg: linear-gradient(
        120.95deg,
        #87da21 2.11%,
        rgba(254, 103, 18, 0) 51.54%,
        #87da21
    );
    background: hsla(0, 0%, 100%, 0.1);
    box-shadow: 0 4px 24px 0 rgba(135, 218, 33, 0.25);
}
.runway-flare.delta-elo[data-v-e0ded02e] {
    border: 1px solid transparent;
    --imBorderBg: linear-gradient(
        120.95deg,
        #87da21 2.11%,
        rgba(135, 218, 33, 0) 51.54%,
        #87da21
    );
    background: rgba(135, 218, 33, 0.15);
    box-shadow: 0 4px 24px 0 rgba(135, 218, 33, 0.25);
}
.runway-flare.delta-elo.negative[data-v-e0ded02e] {
    background: rgba(255, 0, 0, 0.1);
    box-shadow: 0 4px 24px 0 rgba(255, 0, 0, 0.25);
    --imBorderBg: linear-gradient(
        120.95deg,
        red 2.11%,
        rgba(255, 0, 0, 0) 51.54%,
        red
    );
}
@keyframes pulse-shadow-e0ded02e {
    0% {
        box-shadow: 0 4px 24px 0 rgba(var(--box-shadow), 0.15);
    }
    50% {
        box-shadow: 0 4px 24px 0 rgba(var(--box-shadow), 0.45);
    }
    to {
        box-shadow: 0 4px 24px 0 rgba(var(--box-shadow), 0.15);
    }
}
.field[data-v-4f2b75d4] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 0;
}
.field .label[data-v-4f2b75d4] {
    display: flex;
    width: 100%;
    font-size: 0.875rem;
    color: hsla(0, 0%, 100%, 0.8);
}
.field.dark .label[data-v-4f2b75d4] {
    color: hsla(0, 0%, 100%, 0.5);
}
.field .content[data-v-4f2b75d4] {
    font-weight: 400;
}
.loader-text[data-v-4f2b75d4] {
    height: 0.5rem;
    padding: 1rem 0;
    margin: 0;
}
.top3[data-v-7ba9b060] {
    width: 64rem;
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    grid-gap: 1.5rem;
    margin-bottom: 2rem;
}
.finished-lobby-player[data-v-7ba9b060] {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem 2rem;
    border-radius: 0.5rem;
    border: 1px solid var(--White-White-5, hsla(0, 0%, 100%, 0.05));
    background: linear-gradient(
            0deg,
            rgba(255, 140, 196, 0.2),
            rgba(255, 140, 196, 0.2)
        ),
        linear-gradient(100deg, #1a000c 0.38%, #330019 98.25%);
    box-shadow: inset 0 0 0 0.5px hsla(0, 0%, 100%, 0.05),
        0 0 4px 1px rgba(0, 0, 0, 0.25);
}
.finished-lobby-player[data-v-7ba9b060] .content {
    color: #fff;
}
.finished-lobby-player .field[data-v-7ba9b060] {
    flex: 1;
}
.finished-lobby-player .runway-flare[data-v-7ba9b060] {
    padding: 0.125rem 0.625rem;
    font-size: 1.5rem;
}
.finished-lobby-player.hidden[data-v-7ba9b060] {
    opacity: 0;
}
.finished-lobby-player.top-player[data-v-7ba9b060] {
    margin-top: auto;
    padding-top: 2rem;
}
.finished-lobby-player.top-player.index-1[data-v-7ba9b060] {
    padding-top: 6rem;
}
.finished-lobby-player.index-2[data-v-7ba9b060] {
    padding-top: 3rem;
}
.finished-lobby-player[data-v-7ba9b060] .pos-2,
.finished-lobby-player[data-v-7ba9b060] .pos-3 {
    background: rgba(0, 0, 0, 0.5);
}
.finished-lobby-player[data-v-7ba9b060] .pos-1 {
    font-size: 3rem;
    border-radius: 1rem;
    background: linear-gradient(
            0deg,
            hsla(44, 96%, 71%, 0.5),
            hsla(44, 96%, 71%, 0.5)
        ),
        rgba(0, 0, 0, 0.85);
    padding: var(--gap-gap-1, 0.25rem) 1.25rem;
    position: relative;
}
.finished-lobby-player[data-v-7ba9b060] .pos-1 .crown {
    position: absolute;
    width: 7.36rem;
    height: 4.325rem;
    rotate: 15deg;
    top: -58%;
    left: 14%;
}
.finished-lobby-player .top[data-v-7ba9b060] {
    position: absolute;
    top: 0;
    left: 50%;
    translate: -50% -50%;
}
.finished-lobby-player.index-1[data-v-7ba9b060] {
    background: linear-gradient(
            180deg,
            rgba(252, 204, 73, 0.3),
            rgba(252, 204, 73, 0)
        ),
        linear-gradient(
            0deg,
            rgba(255, 140, 196, 0.2),
            rgba(255, 140, 196, 0.2)
        ),
        linear-gradient(100deg, #1a000c 0.38%, #330019 98.25%);
}
.aircraft-indicator[data-v-4929707d] {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 0.25rem;
    background: rgba(29, 29, 29, 0.75);
    gap: 0.5rem;
    padding: 0 0.25rem 0.25rem;
    width: 4rem;
    color: #fff;
    height: 100%;
}
.aircraft-indicator.small[data-v-4929707d] {
    width: 2.25rem;
    height: 6.125rem;
}
.aircraft-indicator .values[data-v-4929707d] {
    flex: 1;
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 0;
}
.aircraft-indicator .values canvas[data-v-4929707d] {
    width: 100%;
    max-height: 100%;
    flex: 1;
}
.aircraft-indicator .values.invert .indicator .value[data-v-4929707d] {
    left: unset;
    right: 0;
    transform: translate(0.1rem, -50%);
}
.aircraft-indicator .values .indicator[data-v-4929707d] {
    position: absolute;
    background: #d9d9d9;
    box-shadow: inset 0 0 3px 0 #87da21;
    filter: drop-shadow(0 0 8px rgba(135, 218, 33, 0.6));
    width: calc(100% + 0.5rem);
    left: -0.25rem;
    top: calc(var(--v) * 100%);
    padding: 0;
    height: 0.15rem;
    border-radius: 0.125rem;
    translate: 0 -50%;
}
.aircraft-indicator .values .indicator[data-v-4929707d]:after,
.aircraft-indicator .values .indicator[data-v-4929707d]:before {
    position: absolute;
    content: "";
    background: #d9d9d9;
    box-shadow: inset 0 0 3px 0 #87da21;
    filter: drop-shadow(0 0 8px rgba(135, 218, 33, 0.6));
    border-radius: inherit;
    width: 0.125rem;
    height: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
}
.aircraft-indicator .values .indicator[data-v-4929707d]:before {
    left: 0;
}
.aircraft-indicator .values .indicator[data-v-4929707d]:after {
    right: 0;
}
.aircraft-indicator .values .indicator .value[data-v-4929707d] {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-0.1rem, -50%);
    z-index: 2;
    padding: 0 0.25rem;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    border-radius: 0.125rem;
    border: 0.5px solid #464646;
    background: #111112;
    color: #87da21;
    text-shadow: 0 0 4px rgba(135, 218, 33, 0.6);
    font-family: Oswald;
    font-size: 1.25rem;
}
.artificial-horizon[data-v-59e99dec] {
    width: 100%;
    height: 100%;
    aspect-ratio: 1;
    border-radius: 50%;
    background: rgba(29, 29, 29, 0.75);
    padding: 0.5rem;
}
.artificial-horizon canvas[data-v-59e99dec] {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: inset 0 0 0.5rem 0 #1d1d1d;
}
.aircraft-hud[data-v-c1f2a352] {
    display: flex;
    gap: 0.75rem;
    height: 15rem;
    align-items: flex-end;
    scale: 0.8;
    transform-origin: bottom center;
}
.aircraft-hud .artificial-horizon-wrapper[data-v-c1f2a352] {
    width: 15rem;
    height: 15rem;
    position: relative;
}
.aircraft-hud .artificial-horizon-wrapper .control[data-v-c1f2a352] {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    gap: 0;
}
.aircraft-hud .artificial-horizon-wrapper .control.right[data-v-c1f2a352] {
    left: unset;
    right: 0;
}
.aircraft-hud .agl-indicator[data-v-c1f2a352] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    color: #fff;
    font-size: 0.75rem;
}
.aircraft-hud .agl-indicator .label[data-v-c1f2a352] {
    display: flex;
    width: -moz-fit-content;
    width: fit-content;
    padding: 0 0.625rem 0.5rem 0.625rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    border-radius: 0.125rem;
    background: rgba(29, 29, 29, 0.75);
}
.aircraft-hud .agl-indicator .value[data-v-c1f2a352] {
    padding: 0 0.25rem;
    border-radius: 0.125rem;
    border: 0.5px solid #464646;
    background: #111112;
    box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.5);
    color: #87da21;
    text-shadow: 0 0 8px rgba(135, 218, 33, 0.6);
    font-family: Oswald;
    font-size: 1.25rem;
}
.hud-bottom[data-v-21eb02df] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    position: absolute;
    left: 50%;
    translate: -50% 0;
    bottom: 2.5rem;
    min-width: 20rem;
}
.rotate-bottom-leave-active[data-v-21eb02df] {
    position: absolute;
}
.leaderboard-entry[data-v-0a8bb2fb] {
    color: #fff;
    display: flex;
    align-items: center;
    width: 18rem;
    gap: 0.625rem;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 0.25rem;
}
.leaderboard-entry .index[data-v-0a8bb2fb] {
    width: 2rem;
    height: 2rem;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    background: rgba(0, 0, 0, 0.5);
}
.leaderboard-entry .index.num-1[data-v-0a8bb2fb] {
    border-radius: 0.25rem;
    border: 1px solid var(--Brand, #87da21);
    background: linear-gradient(
            0deg,
            rgba(135, 218, 33, 0.5),
            rgba(135, 218, 33, 0.5)
        ),
        rgba(0, 0, 0, 0.5);
}
.leaderboard-entry .index.num-2[data-v-0a8bb2fb] {
    border-radius: 0.25rem;
    border: 1px solid var(--Brand, #87da21);
    background: linear-gradient(
            0deg,
            rgba(135, 218, 33, 0.25),
            rgba(135, 218, 33, 0.25)
        ),
        rgba(0, 0, 0, 0.5);
}
.leaderboard-entry .index.num-3[data-v-0a8bb2fb] {
    border-radius: 0.25rem;
    border: 1px solid var(--Brand, #87da21);
    background: linear-gradient(
            0deg,
            rgba(135, 218, 33, 0.15),
            rgba(135, 218, 33, 0.15)
        ),
        rgba(0, 0, 0, 0.5);
}
.leaderboard-entry .nickname[data-v-0a8bb2fb] {
    display: flex;
    gap: 0.25rem;
    height: 100%;
    max-width: 100%;
    align-items: center;
    flex: 1;
    color: hsla(0, 0%, 100%, 0.65);
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.leaderboard-entry .offset[data-v-0a8bb2fb] {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 500;
    width: 5rem;
    padding: 0.5rem 0.625rem;
    border-radius: 0.25rem;
    border: 1px solid rgba(135, 218, 33, 0.5);
    background: linear-gradient(
            0deg,
            rgba(135, 218, 33, 0.05),
            rgba(135, 218, 33, 0.05)
        ),
        rgba(0, 0, 0, 0.2);
}
.leaderboard-entry .offset.red[data-v-0a8bb2fb] {
    border-radius: 0.25rem;
    border: 1px solid var(--Red-Red-50, rgba(255, 0, 0, 0.5));
    background: linear-gradient(
            0deg,
            var(--Red-Red-15, rgba(255, 0, 0, 0.15)) 0,
            var(--Red-Red-15, rgba(255, 0, 0, 0.15)) 100%
        ),
        rgba(0, 0, 0, 0.25);
}
.racing-leaderboard[data-v-7f43903a] {
    transform: rotateY(-8deg);
    transform-origin: right center;
    position: absolute;
    right: 1.5rem;
    top: 50%;
    translate: 0 -50%;
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
}
.racing-leaderboard .dots[data-v-7f43903a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}
.racing-leaderboard .dots .dot[data-v-7f43903a] {
    width: 0.25rem;
    height: 0.25rem;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
}
.racing-info-segment[data-v-4b98a2aa] {
    display: flex;
    align-items: center;
    min-height: 2.5rem;
    padding: 0.25rem 0.75rem;
    width: -moz-fit-content;
    width: fit-content;
    gap: 0.625rem;
    color: #fff;
    font-family: Geist;
    font-size: 1rem;
    font-style: normal;
    font-weight: 500;
    border-radius: 0.5rem;
    background: var(--Black-Black-80, hsla(0, 0%, 7%, 0.8));
}
.racing-info-segment svg[data-v-4b98a2aa] {
    width: 1.5rem;
    height: 1.5rem;
    stroke-width: 2px;
    color: #87da21;
}
.racing-info-segment .label[data-v-4b98a2aa] {
    display: flex;
    padding: 0.375rem 0.625rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: linear-gradient(
            0deg,
            hsla(0, 0%, 100%, 0.05),
            hsla(0, 0%, 100%, 0.05)
        ),
        rgba(0, 0, 0, 0.25);
}
.racing-info-segment .label.red[data-v-4b98a2aa] {
    border-radius: 0.25rem;
    border: 1px solid var(--Red-Red-50, rgba(255, 0, 0, 0.5));
    background: linear-gradient(
            0deg,
            rgba(255, 0, 0, 0.05),
            rgba(255, 0, 0, 0.05)
        ),
        rgba(0, 0, 0, 0.25);
}
.racing-info[data-v-6bc511bb] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.racing-info .row[data-v-6bc511bb] {
    display: flex;
    gap: 0.75rem;
}
.racing-info .time[data-v-6bc511bb] {
    width: 5rem;
}
.icon-message[data-v-7d655889] {
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: var(--Black-Black-80, hsla(0, 0%, 7%, 0.8));
    display: flex;
    align-items: center;
    gap: 1rem;
}
.icon-message svg[data-v-7d655889] {
    flex-shrink: 0;
    margin-bottom: auto;
}
.suggestions[data-v-6d6b6dee] {
    list-style-type: none;
    box-sizing: border-box;
    color: #fff;
    width: 100%;
    margin: 0;
    padding: 0;
}
.suggestion[data-v-6d6b6dee] {
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: var(--Black-Black-80, hsla(0, 0%, 7%, 0.8));
    font-family: Geist;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0 0 0;
}
small.help[data-v-6d6b6dee] {
    display: inline-flex;
    align-items: center;
    color: #aeaeae;
}
small.help .dot[data-v-6d6b6dee] {
    display: inline-flex;
    width: 0.25rem;
    height: 0.25rem;
    border-radius: 50%;
    background: #aeaeae;
    margin-right: 0.25rem;
}
.disabled[data-v-6d6b6dee] {
    color: #aeaeae;
}
.default-chat-message[data-v-8e299d62] {
    font-family: Geist;
    font-size: 0.875rem;
}
.default-chat-message[data-v-8e299d62] svg {
    stroke-width: 2;
    color: #87da21;
}
.default-chat-message .author[data-v-8e299d62] {
    color: #aeaeae;
    margin-right: 0.25rem;
}
.default-chat-message .content[data-v-8e299d62] {
    color: var(--White-White, #fff);
    font-weight: 500;
    white-space: pre-line;
}
.success-message[data-v-61693b04] {
    border-radius: 0.25rem;
    border: 1px solid var(--Brand-Brand, #87da21);
    background: var(--Brand-Brand-80, rgba(135, 218, 33, 0.8));
    color: var(--Black-Black, #121212);
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 700;
}
.success-message[data-v-61693b04] svg {
    stroke-width: 2;
    color: #121212;
}
.warning-message[data-v-2200ad0c] {
    border-radius: 0.25rem;
    border: 1px solid var(--Yellow-Yellow, #ffd600);
    background: var(--Yellow-Yellow-80, rgba(255, 214, 0, 0.8));
    color: var(--Black-Black, #121212);
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 700;
}
.warning-message[data-v-2200ad0c] svg {
    stroke-width: 2;
    color: #121212;
}
.med-message[data-v-8b7ce7c4] {
    font-family: Geist;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    border: 1px solid var(--Red-Red, red);
    background: rgba(31, 0, 0, 0.9);
}
.med-message[data-v-8b7ce7c4] svg {
    stroke-width: 2;
    color: #fff;
}
.med-message .author[data-v-8b7ce7c4] {
    color: #d8abab;
    margin-right: 0.25rem;
}
.med-message .content[data-v-8b7ce7c4] {
    color: var(--White-White, #fff);
    font-weight: 500;
}
.status-message[data-v-1baabcf9] {
    font-family: Geist;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: rgba(1, 16, 59, 0.9);
}
.status-message[data-v-1baabcf9] svg {
    stroke-width: 2;
    color: #fff;
}
.status-message .author[data-v-1baabcf9] {
    color: #9fa3ad;
    margin-right: 0.25rem;
}
.status-message .content[data-v-1baabcf9] {
    color: var(--White-White, #fff);
    font-weight: 500;
}
.me-message[data-v-2bec664b] {
    font-family: Geist;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: hsla(0, 0%, 6%, 0.9);
}
.me-message[data-v-2bec664b] svg {
    stroke-width: 2;
    color: #fff;
}
.me-message .author[data-v-2bec664b] {
    color: #9fa3ad;
    margin-right: 0.25rem;
}
.me-message .content[data-v-2bec664b] {
    color: var(--White-White, #fff);
    font-weight: 500;
}
.do-message[data-v-1f256e76] {
    font-family: Geist;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    border: 1px solid var(--Red-Red, red);
    background: rgba(198, 1, 1, 0.75);
    box-shadow: 0 4px 12px 0 rgba(255, 0, 0, 0.55);
}
.do-message.success[data-v-1f256e76] {
    border-radius: 0.25rem;
    border: 1px solid #46da21;
    background: rgba(45, 146, 20, 0.75);
    box-shadow: 0 4px 12px 0 rgba(70, 218, 33, 0.55);
}
.do-message[data-v-1f256e76] svg {
    stroke-width: 2;
    color: #fff;
}
.do-message .author[data-v-1f256e76] {
    color: #fff;
    margin-right: 0.25rem;
}
.do-message .content[data-v-1f256e76] {
    color: var(--White-White, #fff);
    font-weight: 500;
}
.message-911[data-v-d3e1290e] {
    font-family: Geist;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    -o-border-image: linear-gradient(90deg, red, #2094ff) 1;
    border-image: linear-gradient(90deg, red, #2094ff) 1;
    border-width: 2px;
    border-style: solid;
    background: linear-gradient(
            90deg,
            rgba(255, 0, 0, 0.1),
            rgba(32, 148, 255, 0.1)
        ),
        hsla(0, 0%, 9%, 0.75);
}
.message-911 .bold[data-v-d3e1290e] {
    font-weight: 500;
    color: hsla(0, 0%, 100%, 0.65);
}
.message-911[data-v-d3e1290e] svg {
    stroke-width: 2;
    color: #fff;
    margin-top: 0.25rem;
}
.message-911 .author[data-v-d3e1290e] {
    color: #d8abab;
    margin-right: 0.25rem;
}
.message-911 .content[data-v-d3e1290e] {
    color: var(--White-White, #fff);
    font-weight: 500;
}
.resp-911[data-v-9aa7522a] {
    font-family: Geist;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    -o-border-image: linear-gradient(-90deg, red, #2094ff) 1;
    border-image: linear-gradient(-90deg, red, #2094ff) 1;
    border-width: 2px;
    border-style: solid;
    background: linear-gradient(
            -90deg,
            rgba(255, 0, 0, 0.1),
            rgba(32, 148, 255, 0.1)
        ),
        hsla(0, 0%, 9%, 0.75);
}
.resp-911 .bold[data-v-9aa7522a] {
    font-weight: 500;
    color: hsla(0, 0%, 100%, 0.65);
}
.resp-911[data-v-9aa7522a] svg {
    stroke-width: 2;
    color: #fff;
    margin-top: 0.25rem;
}
.resp-911 .author[data-v-9aa7522a] {
    color: #d8abab;
    margin-right: 0.25rem;
}
.resp-911 .content[data-v-9aa7522a] {
    color: var(--White-White, #fff);
    font-weight: 500;
}
.gov-card-message[data-v-30724db6] {
    padding: var(--icon-size, 1.5rem);
    border-radius: var(--icon-size, 1.5rem);
    background: hsla(0, 0%, 9%, 0.95);
    position: relative;
    isolation: isolate;
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 2fr);
    grid-gap: 1.25rem;
}
.gov-card-message .background[data-v-30724db6] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.gov-card-message .background img[data-v-30724db6] {
    position: absolute;
    bottom: 0;
    right: 0;
}
.gov-card-message .col-image[data-v-30724db6] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 100%;
}
.gov-card-message .col-image .profile[data-v-30724db6] {
    width: 100%;
    height: 10.375rem;
    border-radius: 0.5rem;
    background: #1f1e1e;
    display: flex;
}
.gov-card-message .col-image .profile svg[data-v-30724db6] {
    color: #3e3b3b;
    font-size: 4rem;
    font-weight: 100;
    margin: auto;
}
.gov-card-message .col-image .profile img[data-v-30724db6] {
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.gov-card-message .col-image img[data-v-30724db6] {
    width: 100%;
}
.gov-card-message .col[data-v-30724db6] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.gov-card-message .col .spacer[data-v-30724db6] {
    height: 1px;
    width: 100%;
    background: #3d3d3d;
}
.gov-card-message .col .green[data-v-30724db6] {
    color: #87d921;
}
.gov-card-message .col .dark[data-v-30724db6] {
    color: hsla(0, 0%, 100%, 0.6);
}
.gov-card-message .col h2[data-v-30724db6] {
    font-size: 1.75rem;
    font-weight: 400;
    line-height: 100%;
}
.gov-card-message .col p[data-v-30724db6] {
    font-size: 0.75rem;
    line-height: 100%;
}
.gov-card-message .col .row[data-v-30724db6] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.business-card-message[data-v-7cba12b2] {
    padding: var(--icon-size, 1.5rem);
    border-radius: var(--icon-size, 1.5rem);
    background: linear-gradient(
            107deg,
            hsla(0, 0%, 7%, 0.95) 1.2%,
            rgba(33, 31, 35, 0.95) 98.2%
        ),
        url() #d3d3d3 0 0/100px 100px repeat;
    position: relative;
    isolation: isolate;
}
.business-card-message .col[data-v-7cba12b2] {
    display: flex;
    flex-direction: column;
    gap: 1.6rem;
    width: 100%;
}
.business-card-message .col .name[data-v-7cba12b2] {
    color: #fff;
    font-family: Oswald;
    font-size: 2.25rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.business-card-message .col .workplaces[data-v-7cba12b2] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.business-card-message .col .spacer[data-v-7cba12b2] {
    height: 1px;
    width: 100%;
    background: #3d3d3d;
}
.business-card-message .col .green[data-v-7cba12b2] {
    color: #87d921;
}
.business-card-message .col .dark[data-v-7cba12b2] {
    color: hsla(0, 0%, 100%, 0.6);
}
.business-card-message .col .white[data-v-7cba12b2] {
    color: #eee;
}
.business-card-message .col .uppercase[data-v-7cba12b2] {
    text-transform: uppercase;
}
.business-card-message .col h1[data-v-7cba12b2] {
    font-size: 2.25rem;
    font-weight: 400;
    line-height: 100%;
}
.business-card-message .col h2[data-v-7cba12b2] {
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 100%;
}
.business-card-message .col h3[data-v-7cba12b2] {
    font-size: 1.3125rem;
    font-weight: 400;
    line-height: 100%;
}
.business-card-message .col p[data-v-7cba12b2] {
    font-size: 0.75rem;
    line-height: 100%;
}
.business-card-message .col .row[data-v-7cba12b2] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}
.business-card-message .job-details[data-v-7cba12b2] {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}
.business-card-message .job-details .white[data-v-7cba12b2] {
    color: #eee;
    font-size: 0.85rem;
    font-weight: 400;
}
.security-card-message[data-v-25185f66] {
    padding: var(--icon-size, 1.5rem);
    border-radius: var(--icon-size, 1.5rem);
    background: linear-gradient(
        180deg,
        rgba(21, 25, 30, 0.95),
        hsla(0, 0%, 9%, 0.95)
    );
    position: relative;
    isolation: isolate;
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 2fr);
    grid-gap: 1.25rem;
}
.security-card-message .background[data-v-25185f66] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.security-card-message .background svg[data-v-25185f66] {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    width: 15rem;
    height: 15rem;
}
.security-card-message .col-image[data-v-25185f66] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 100%;
}
.security-card-message .col-image .profile[data-v-25185f66] {
    width: 100%;
    height: 10.375rem;
    border-radius: 0.5rem;
    background: #1f1e1e;
    display: flex;
}
.security-card-message .col-image .profile svg[data-v-25185f66] {
    color: #3e3b3b;
    font-size: 4rem;
    font-weight: 100;
    margin: auto;
}
.security-card-message .col-image .profile img[data-v-25185f66] {
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.security-card-message .col-image img[data-v-25185f66] {
    width: 100%;
}
.security-card-message .col[data-v-25185f66] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.security-card-message .col .spacer[data-v-25185f66] {
    height: 1px;
    width: 100%;
    background: #3d3d3d;
}
.security-card-message .col .green[data-v-25185f66] {
    color: #2171d9;
}
.security-card-message .col .dark[data-v-25185f66] {
    color: hsla(0, 0%, 100%, 0.6);
}
.security-card-message .col h2[data-v-25185f66] {
    font-size: 1.75rem;
    font-weight: 400;
    line-height: 100%;
}
.security-card-message .col p[data-v-25185f66] {
    font-size: 0.75rem;
    line-height: 100%;
}
.security-card-message .col .row[data-v-25185f66] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.msg[data-v-cf021cce]:not(:last-of-type) {
    margin-bottom: 2px;
    margin-right: 5px;
}
.chat-wrapper[data-v-46b2aca9] {
    width: 30rem;
    color: #fff;
}
.chat-wrapper .color-0[data-v-46b2aca9] {
    color: #fff;
}
.chat-wrapper .color-1[data-v-46b2aca9] {
    color: #6e1616;
}
.chat-wrapper .color-2[data-v-46b2aca9] {
    color: #52984a;
}
.chat-wrapper .color-3[data-v-46b2aca9] {
    color: #f09348;
}
.chat-wrapper .color-4[data-v-46b2aca9] {
    color: #b40000;
}
.chat-wrapper .color-5[data-v-46b2aca9] {
    color: #13141f;
}
.chat-wrapper .color-6[data-v-46b2aca9] {
    color: #a6c;
}
.chat-wrapper .color-8[data-v-46b2aca9] {
    color: #c00;
}
.chat-wrapper .color-9[data-v-46b2aca9] {
    color: #cc0068;
}
.chat-wrapper .chat-messages[data-v-46b2aca9] {
    font-size: 1rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 0.325rem;
    max-height: 17.5rem;
    margin-top: -1rem;
    padding-top: 1rem;
    -webkit-mask-image: linear-gradient(
        0deg,
        #fff calc(100% - 1rem),
        transparent
    );
    mask-image: linear-gradient(0deg, #fff calc(100% - 1rem), transparent);
}
.chat-wrapper .chat-input[data-v-46b2aca9] {
    margin-top: 0.25rem;
    box-sizing: border-box;
}
.chat-wrapper .chat-input textarea[data-v-46b2aca9] {
    color: #fff;
    width: 100%;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    resize: none;
    font-family: Geist;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: var(--Black-Black-80, hsla(0, 0%, 7%, 0.8));
    padding: 0.75rem 1rem;
}
.chat-wrapper .chat-input textarea[data-v-46b2aca9]:focus {
    outline: none;
}
.chat-wrapper .chat-message[data-v-46b2aca9] {
    display: inline-block !important;
    padding: 0.5vw;
    border-radius: 4px;
    background-color: rgba(222, 51, 51, 0.8);
    border: 1px solid hsla(0, 0%, 100%, 0.23);
    width: -moz-fit-content;
    width: fit-content;
    max-width: 95%;
    overflow: hidden;
    word-break: break-word;
    position: relative;
}
.chat-wrapper .chat-message-header[data-v-46b2aca9] {
    border-bottom: 1px solid hsla(0, 0%, 100%, 0.23);
    padding: 5px;
}
.chat-wrapper .chat-message-body[data-v-46b2aca9] {
    margin-top: 10px;
}
.chat-wrapper .chat-message.ooc[data-v-46b2aca9] {
    background-color: rgba(77, 77, 77, 0.8);
}
.chat-wrapper .chat-message.pooc.isStaff[data-v-46b2aca9]:before {
    content: "Staff";
    position: absolute;
    right: 2.5%;
    top: 20%;
    opacity: 0.5;
}
.chat-wrapper .chat-message.pooc[data-v-46b2aca9] {
    background-color: rgba(7, 49, 218, 0.8);
}
.chat-wrapper .chat-message.ooc.isStaff[data-v-46b2aca9]:before {
    content: "Staff";
    position: absolute;
    right: 2.5%;
    top: 20%;
    opacity: 0.5;
}
.chat-wrapper .chat-message.advert[data-v-46b2aca9] {
    background-color: rgba(47, 92, 115, 0.8);
}
.chat-wrapper .chat-message.server[data-v-46b2aca9] {
    background-color: rgba(240, 200, 80, 0.8);
}
.chat-wrapper .chat-message.system[data-v-46b2aca9] {
    background-color: #43396f;
}
.chat-wrapper .chat-message.emergency[data-v-46b2aca9] {
    background-color: rgba(224, 50, 50, 0.8);
}
.chat-wrapper .chat-message.nonemergency[data-v-46b2aca9] {
    background-color: rgba(112, 25, 25, 0.8);
}
.chat-wrapper .chat-message.help[data-v-46b2aca9] {
    background-color: rgba(109, 80, 240, 0.8);
    color: #fff;
}
.chat-wrapper .chat-message.e911[data-v-46b2aca9] {
    background-color: rgba(36, 123, 165, 0.78);
    color: #fff;
}
.chat-wrapper .chat-message.e311[data-v-46b2aca9] {
    background-color: rgba(118, 0, 54, 0.78);
    color: #fff;
}
.chat-wrapper .chat-message.dispatch[data-v-46b2aca9] {
    background-color: rgba(0, 184, 138, 0.78);
    color: #fff;
}
.chat-wrapper .chat-message.dispatch .chat-message-body[data-v-46b2aca9] {
    white-space: pre-wrap;
}
.hud-top-left[data-v-af4c8934] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    transform: rotateY(8deg);
    transform-origin: 0 0;
    position: absolute;
    top: 2.5rem;
    left: 1.5rem;
    backface-visibility: hidden;
}
.slide-left-leave-active[data-v-af4c8934] {
    position: absolute;
}
.runway-theme[data-v-3c33d524] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 17rem;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0.25) 2.22%,
            rgba(252, 112, 197, 0.55)
        ),
        rgba(0, 0, 0, 0.75);
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(45deg, transparent, #fc70c5);
    color: #fc70c5;
    text-shadow: 0 4px 24px rgba(252, 112, 197, 0.5);
    font-family: Oswald;
    font-size: 2rem;
}
.runway-theme .dark[data-v-3c33d524] {
    color: var(--White-80, hsla(0, 0%, 100%, 0.8));
    font-family: Oswald;
    font-size: 1rem;
    font-weight: 400;
    text-shadow: none;
}
.hud-left[data-v-41cbceca] {
    position: absolute;
    top: 50%;
    left: 2.5rem;
    translate: 0 -50%;
    transform-origin: left center;
    transform: rotateY(8deg);
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.rotate-in-enter-active[data-v-41cbceca],
.rotate-in-leave-active[data-v-41cbceca],
.rotate-in-move[data-v-41cbceca] {
    transform-origin: left center;
    transition: all 0.5s ease;
}
.rotate-in-enter-from[data-v-41cbceca],
.rotate-in-leave-to[data-v-41cbceca] {
    opacity: 0;
    transform: rotateX(-90deg) translateY(-100%);
}
.rotate-in-leave-active[data-v-41cbceca] {
    position: absolute;
}
.wrapper[data-v-9c966dc2] {
    perspective: var(--perspective);
}
.wrapper[data-v-9c966dc2] .section {
    display: flex;
    align-items: center;
    padding: 0.5rem var(--gap-gap-4, 1rem);
    gap: 0.375rem;
    border-radius: 0.75rem;
    background: rgba(0, 0, 0, 0.5);
}
.wrapper[data-v-9c966dc2] .section svg {
    color: #87da21;
    stroke-width: 2;
}
.wrapper[data-v-9c966dc2] .section p {
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 100%;
}
.x-hair[data-v-9c966dc2] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.45rem;
    height: 0.45rem;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.75);
    background: hsla(0, 0%, 100%, 0.75);
}
.particles[data-v-9c966dc2] {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none !important;
    width: 100%;
    height: 100%;
}
.wrapper[data-v-4b5dbbc4] {
    perspective: var(--perspective);
}
.inner-wrapper[data-v-4b5dbbc4] {
    width: 100%;
    height: 100%;
    position: relative;
    transform: rotateY(var(--rotation));
    transform-origin: center center;
    pointer-events: none !important;
}
canvas[data-v-4b5dbbc4] {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
}
@property --dg {
    syntax: "<color>";
    initial-value: rgba(135, 218, 33, 0.05);
    inherits: false;
}
.menu[data-v-4b5dbbc4],
canvas[data-v-4b5dbbc4] {
    width: 100%;
    height: 100%;
    pointer-events: none !important;
}
.menu[data-v-4b5dbbc4] {
    position: relative;
    isolation: isolate;
}
.menu .item[data-v-4b5dbbc4] {
    pointer-events: auto;
    border-radius: 0.25rem;
    border: 1px solid var(--Brand-Brand, #87da21);
    background: linear-gradient(0deg, var(--dg) 0, var(--dg) 100%),
        rgba(0, 0, 0, 0.55);
    width: -moz-fit-content;
    width: fit-content;
    cursor: pointer;
    position: absolute;
    top: var(--t);
    left: var(--l);
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.5rem;
    color: var(--white-100, #fff);
    font-family: Geist;
    font-size: 0.875rem;
    font-style: normal;
    min-width: 13rem;
    font-weight: 500;
}
.menu .item.noHover[data-v-4b5dbbc4] {
    pointer-events: none;
}
.menu .item svg[data-v-4b5dbbc4] {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--Green---Active, #87da21);
}
.menu .item[data-v-4b5dbbc4]:hover {
    --dg: rgba(157, 255, 0, 0.1);
}
.menu .item.left[data-v-4b5dbbc4] {
    translate: -100% -50%;
}
.menu .item.right[data-v-4b5dbbc4] {
    translate: 0 -50%;
}
.menu .item.center[data-v-4b5dbbc4] {
    justify-content: center;
    translate: -50% -100%;
}
.menu .item.center.down[data-v-4b5dbbc4] {
    translate: -50% 0;
}
.fade-anim-enter-active[data-v-4b5dbbc4],
.fade-anim-leave-active[data-v-4b5dbbc4],
.fade-anim-move[data-v-4b5dbbc4] {
    transition: all 0.2s ease;
}
.fade-anim-enter-active[data-v-4b5dbbc4] {
    transition-delay: var(--delay, 0ms);
}
.fade-anim-enter-from[data-v-4b5dbbc4],
.fade-anim-leave-to[data-v-4b5dbbc4] {
    opacity: 0 !important;
}
.fade-anim-leave-active[data-v-4b5dbbc4] {
    position: absolute;
}
.notification[data-v-b47b48c8] {
    position: relative;
    width: -moz-fit-content;
    width: fit-content;
    margin-left: auto;
    padding: 0.75rem 1rem;
    color: #fff;
    max-width: 25rem;
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    --bg: #001620;
    --border: #33454d;
    --accentColor: #00b2ff;
    border-radius: 0.25rem;
    background: var(--bg);
    border: 1px solid var(--border);
    --accentWidth: 2px;
    --accent: 0.75rem;
}
.notification.error[data-v-b47b48c8] {
    --bg: #7d000080;
    --border: #734e5a;
    --accentColor: red;
}
.notification.success[data-v-b47b48c8] {
    --bg: #39620680;
    --border: #64744d;
    --accentColor: #87da21;
}
.notification h2[data-v-b47b48c8] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 100%;
}
.notification h3[data-v-b47b48c8] {
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 100%;
    white-space: pre-line;
}
.dispatch-call[data-v-2fe5f79c] {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
    max-width: 25rem;
    --imBorderWidth: 1px;
    border-radius: 0.5rem;
    color: #fff;
    font-family: Geist;
    transition: box-shadow 0.5s ease;
}
.dispatch-call.blink[data-v-2fe5f79c] {
    box-shadow: 0 0 0 0.125rem #87da21;
}
.dispatch-call .dark[data-v-2fe5f79c] {
    color: hsla(0, 0%, 100%, 0.5);
}
.dispatch-call.prio-0[data-v-2fe5f79c] {
    background: linear-gradient(
            92deg,
            rgba(0, 133, 255, 0.15),
            rgba(255, 0, 0, 0.08) 99.89%
        ),
        rgba(0, 0, 0, 0.85);
    border: 1px solid transparent;
    --imBorderBg: linear-gradient(
        180deg,
        hsla(0, 0%, 100%, 0.15),
        rgba(0, 163, 255, 0.85)
    );
}
.dispatch-call.prio-1[data-v-2fe5f79c] {
    background: linear-gradient(
            139deg,
            rgba(255, 0, 0, 0.13) 23.4%,
            rgba(0, 133, 255, 0.25) 75.59%
        ),
        rgba(0, 0, 0, 0.85);
}
.dispatch-call.prio-1[data-v-2fe5f79c],
.dispatch-call.prio-2[data-v-2fe5f79c] {
    border: 1px solid transparent;
    --imBorderBg: linear-gradient(
        127.01deg,
        rgba(254, 87, 87, 0.5) 0.68%,
        rgba(105, 183, 254, 0.5)
    );
}
.dispatch-call.prio-2[data-v-2fe5f79c] {
    background: linear-gradient(
            92deg,
            rgba(255, 0, 0, 0.3),
            rgba(0, 133, 255, 0.6) 99.89%
        ),
        rgba(0, 0, 0, 0.85);
}
.dispatch-call .title[data-v-2fe5f79c] {
    font-style: normal;
    font-weight: 500;
}
.dispatch-call .rows[data-v-2fe5f79c] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.dispatch-call .rows .row[data-v-2fe5f79c] {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
.dispatch-call .rows .row svg[data-v-2fe5f79c] {
    color: hsla(0, 0%, 100%, 0.5);
    stroke-width: 2;
    margin-right: -0.25rem;
}
.dispatch-call .rows .separator[data-v-2fe5f79c] {
    width: 100%;
    height: 1px;
    background: hsla(0, 0%, 100%, 0.15);
}
.dispatch-call .units-wrapper[data-v-2fe5f79c] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.dispatch-call .units-wrapper .row[data-v-2fe5f79c] {
    font-size: 0.75rem;
    color: hsla(0, 0%, 100%, 0.8);
}
.dispatch-call .units-wrapper .row .bold[data-v-2fe5f79c] {
    font-weight: 500;
    font-size: 0.75rem;
}
.dispatch-call .units-wrapper .row .invalid[data-v-2fe5f79c] {
    color: red;
}
.dispatch-call .units-wrapper .units[data-v-2fe5f79c] {
    display: flex;
    flex-flow: row wrap;
    gap: 0.5rem;
}
.dispatch-call .units-wrapper .units .unit[data-v-2fe5f79c] {
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-rounded, 0.25rem);
    border: 1px solid hsla(0, 0%, 100%, 0.5);
    font-size: 0.875rem;
    font-weight: 500;
}
.dispatch-call .buttons[data-v-2fe5f79c] {
    display: flex;
    gap: 0.6125rem;
}
.dispatch-call .buttons button[data-v-2fe5f79c] {
    border-radius: var(--border-radius-rounded, 0.25rem);
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: rgba(0, 0, 0, 0.5);
    transition: background 0.2s ease;
    cursor: pointer;
    padding: 0.25rem 0.625rem;
    line-height: 150%;
    font-weight: 500;
    color: #fff;
}
.dispatch-call .buttons button[data-v-2fe5f79c]:hover {
    background: rgba(0, 0, 0, 0.75);
}
.dispatch-call .buttons button[data-v-2fe5f79c]:focus-within {
    outline: none;
}
.wrapper[data-v-3b265152] {
    perspective: var(--perspective);
}
.notifications[data-v-3b265152] {
    display: flex;
    flex-direction: column;
    transform: rotateY(-8deg);
    transform-origin: right top;
    pointer-events: none !important;
    gap: 1.5rem;
    width: 20rem;
    min-height: 5rem;
    position: absolute;
    top: 3.5rem;
    right: 1.75rem;
}
.notifications > .notification[data-v-3b265152] {
    pointer-events: auto;
}
.notification-enter-active[data-v-3b265152],
.notification-leave-active[data-v-3b265152],
.notification-move[data-v-3b265152] {
    transition: opacity 0.5s ease, transform 0.5s ease;
}
.notification-enter-from[data-v-3b265152],
.notification-leave-to[data-v-3b265152] {
    opacity: 0;
}
.notification-enter-from[data-v-3b265152] {
    transform: translateX(100%);
}
.interaction-item[data-v-1e5e066e] {
    position: absolute;
    width: 5rem;
    height: 5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.75rem;
    border: 1.5px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: rgba(26, 31, 20, 0.75);
    translate: -50% -50%;
    cursor: pointer;
    top: 50%;
    left: 50%;
}
.interaction-item img[data-v-1e5e066e],
.interaction-item svg[data-v-1e5e066e] {
    width: 2rem;
    height: 2rem;
    color: #fff;
}
.interaction-item.active .out-text[data-v-1e5e066e],
.interaction-item.active[data-v-1e5e066e]:after,
.interaction-item.active[data-v-1e5e066e]:before,
.interaction-item:hover .out-text[data-v-1e5e066e],
.interaction-item[data-v-1e5e066e]:hover:after,
.interaction-item[data-v-1e5e066e]:hover:before {
    opacity: 1;
}
.interaction-item .out-text[data-v-1e5e066e],
.interaction-item[data-v-1e5e066e]:after,
.interaction-item[data-v-1e5e066e]:before {
    opacity: 0;
    transition: opacity 0.2s ease;
}
.interaction-item[data-v-1e5e066e]:before {
    content: "";
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: absolute;
    top: -1px;
    left: -1px;
    background: radial-gradient(
        circle at var(--x) var(--y),
        rgba(135, 218, 33, 0.35) 0,
        transparent 50%
    );
}
.interaction-item[data-v-1e5e066e]:after {
    content: "";
    position: absolute;
    border-radius: inherit;
    width: 100%;
    padding: 2px;
    height: 100%;
    background: radial-gradient(
        circle at var(--x) var(--y),
        #87da21 0,
        transparent 80%
    );
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
}
.interaction-item .out-text[data-v-1e5e066e] {
    position: absolute;
    color: #fff;
    font-size: 1.25rem;
    white-space: nowrap;
    text-transform: uppercase;
    --offsetX: 0%;
    --offsetY: 0%;
    left: 50%;
    top: 50%;
    translate: calc(var(--dx) * 6rem / 2 + var(--offsetX))
        calc(var(--dy) * 6rem / 2 + var(--offsetY));
}
.interaction-item .out-text.top[data-v-1e5e066e] {
    --offsetY: -100%;
}
.interaction-item .out-text.bottom[data-v-1e5e066e] {
    --offsetY: 0%;
}
.interaction-item .out-text.y-center[data-v-1e5e066e] {
    --offsetY: -50%;
}
.interaction-item .out-text.left[data-v-1e5e066e] {
    --offsetX: -100%;
}
.interaction-item .out-text.right[data-v-1e5e066e] {
    --offsetX: 0%;
}
.interaction-item .out-text.x-center[data-v-1e5e066e] {
    --offsetX: -50%;
}
.back-button[data-v-68dda301] {
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    border: 1.5px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: rgba(26, 31, 20, 0.75);
    color: #fff;
    cursor: pointer;
}
.back-button svg[data-v-68dda301] {
    stroke-width: 3;
}
.wrapper[data-v-9f30e7ea] {
    perspective: var(--perspective);
}
.inner-wrapper[data-v-9f30e7ea] {
    width: 100%;
    height: 100%;
    perspective: var(--perspective);
    transform-origin: right center;
    transform: rotateY(-8deg);
}
.radial-menu-wrapper[data-v-9f30e7ea] {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 70%;
    translate: -50% -50%;
    width: 60rem;
    height: 60rem;
    background: radial-gradient(
        circle,
        rgba(0, 0, 0, 0.25) 0,
        rgba(218, 196, 5, 0) 44%
    );
}
.radial-menu-wrapper .center[data-v-9f30e7ea] {
    position: absolute;
    left: var(--x);
    top: var(--y);
    translate: -50% -50%;
}
.radial-menu-wrapper .center .center-text[data-v-9f30e7ea] {
    color: #fff;
    font-family: Oswald;
    font-size: 1.5rem;
    text-transform: uppercase;
    white-space: pre-wrap;
}
.slide-up-enter-active[data-v-9f30e7ea],
.slide-up-leave-active[data-v-9f30e7ea] {
    transition: all 0.05s ease-in-out;
}
.slide-up-enter-from[data-v-9f30e7ea] {
    opacity: 0;
    transform: translateY(2rem);
}
.slide-up-leave-to[data-v-9f30e7ea] {
    opacity: 0;
    transform: translateY(-2rem);
}
.list-enter-active[data-v-9f30e7ea],
.list-leave-active[data-v-9f30e7ea],
.list-move[data-v-9f30e7ea] {
    transition: top 0.25s ease, left 0.25s ease, opacity 0.25s ease;
    pointer-events: none;
}
.list-enter-from[data-v-9f30e7ea],
.list-leave-to[data-v-9f30e7ea] {
    opacity: 0;
    top: 50% !important;
    left: 50% !important;
}
.prp-button[data-v-e8fd866c] {
    display: flex;
    padding: 0.75rem 1rem;
    height: -moz-fit-content;
    height: fit-content;
    align-items: center;
    justify-content: center;
    width: -moz-fit-content;
    width: fit-content;
    gap: 0.6125rem;
    color: var(--White, #fff);
    font-family: Oswald;
    font-size: 1rem;
    cursor: pointer;
    line-height: 1;
    transition: background 0.2s ease;
    border-radius: var(--XXS, 0.5rem);
    border: 1px solid var(--White-White-5, hsla(0, 0%, 100%, 0.05));
    background: var(--White-White-10, hsla(0, 0%, 100%, 0.1));
}
.prp-button[data-v-e8fd866c]:hover {
    background: var(--Dark-Blue---15, rgba(0, 6, 23, 0.35));
}
.prp-button.big[data-v-e8fd866c] {
    font-size: 1.125rem;
    line-height: 1.125rem;
}
.prp-button.small[data-v-e8fd866c] {
    padding: 0.5rem;
}
.prp-button.disabled[data-v-e8fd866c] {
    filter: contrast(0.5);
    pointer-events: none;
}
.prp-button.red[data-v-e8fd866c] {
    border-radius: 0.375rem;
    border: 1.5px solid var(--Red-Red, red);
    background: var(--Red-Red-15, rgba(255, 0, 0, 0.15));
}
.prp-button.red[data-v-e8fd866c]:hover {
    background: rgba(255, 0, 0, 0.2);
}
.prp-button.green[data-v-e8fd866c] {
    border-radius: 0.375rem;
    border: 1.5px solid var(--green-active, #87da21);
    background: var(--button-bg, #364d21);
}
.prp-button.green[data-v-e8fd866c]:hover {
    background: var(--button-bg-hover, rgba(135, 218, 33, 0.2));
}
.prp-button.block[data-v-e8fd866c] {
    width: 100%;
}
.alert-dialog[data-v-3cb3129f] {
    --accent: 1.5rem;
    --accentWidth: 2px;
    --accentColor: #87da21;
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    translate: -50% -50%;
    padding: var(--gap-gap-4, 1rem) var(--gap-gap-3, 0.75rem);
    border-radius: 0.375rem;
    background: rgba(26, 31, 20, 0.75);
    min-width: 15rem;
    max-width: 25rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.alert-dialog[data-v-3cb3129f] img {
    max-width: 100%;
    max-height: 100%;
}
.alert-dialog.center[data-v-3cb3129f] {
    align-items: center;
    text-align: center;
}
.alert-dialog .buttons[data-v-3cb3129f] {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}
.prp-input[data-v-c6d34f4c] {
    width: 100%;
    border-radius: var(--XXS, 0.5rem);
    background: var(--input-bg);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 13rem;
    padding: 0 1rem;
}
.prp-input.big input[data-v-c6d34f4c] {
    padding: 0.75rem 0;
    font-size: 1rem;
}
.prp-input.big svg[data-v-c6d34f4c] {
    font-size: 1.3125rem;
}
.prp-input.disabled[data-v-c6d34f4c] {
    pointer-events: none;
}
.prp-input.disabled input[data-v-c6d34f4c] {
    color: hsla(0, 0%, 100%, 0.5);
}
.prp-input svg[data-v-c6d34f4c] {
    color: #fff;
    margin-right: -0.25rem;
    font-size: 1rem;
    transition: color 0.1s ease;
}
.prp-input.clickableIcon svg[data-v-c6d34f4c] {
    cursor: pointer;
}
.prp-input.focusable[data-v-c6d34f4c] {
    border: 2px solid transparent;
    transition: border 0.1s ease;
}
.prp-input.focusable[data-v-c6d34f4c]:focus-within {
    border: 2px solid var(--green-active);
    box-shadow: 0 0 1px 0 var(--green-active) inset,
        0 0 1px 0 var(--green-active);
}
.prp-input.focusable:focus-within svg[data-v-c6d34f4c] {
    color: var(--green-active);
}
.prp-input input[data-v-c6d34f4c] {
    width: 100%;
    height: 100%;
    padding: 0.5rem 0;
    border: none;
    background-color: transparent;
    color: #fff;
    font-family: inherit;
    font-size: 1rem;
}
.prp-input input[data-v-c6d34f4c]:focus {
    outline: none;
}
input[type="time"][data-v-c6d34f4c]::-webkit-calendar-picker-indicator {
    display: none;
}
input[type="time"][data-v-c6d34f4c]::-webkit-clear-button,
input[type="time"][data-v-c6d34f4c]::-webkit-inner-spin-button {
    display: none;
}
.prp-textarea {
    font-family: inherit;
    font-size: 1rem;
    border-radius: var(--XXS, 0.5rem);
    border: none;
    background: #0b1203;
    padding: 0.5rem 1rem;
    color: #fff;
    resize: none;
    flex-shrink: 0;
}
.prp-textarea:focus {
    outline: none;
}
.input-field[data-v-27194aae] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.input-field .label[data-v-27194aae] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
    transition: color 0.1s ease;
}
.input-field[data-v-27194aae] .prp-input {
    outline: 1px solid transparent;
    transition: outline-color 0.1s ease;
}
.input-field:focus-within .label[data-v-27194aae] {
    color: #87da21;
}
.input-field[data-v-27194aae]:focus-within .prp-input {
    outline: 1px solid var(--Brand, #87da21);
}
.input-field .description[data-v-27194aae] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.prp-checkbox {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: #0b1203;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 0.325rem;
    margin: 0;
    position: relative;
    cursor: pointer;
}
.prp-checkbox:disabled {
    pointer-events: none;
    filter: contrast(0.5);
}
.prp-checkbox:before {
    content: "";
    display: block;
    width: 0.75rem;
    height: 0.75rem;
    transition: scale 0.12s ease-in-out;
    background: var(--green-active);
    position: absolute;
    left: 50%;
    top: 50%;
    translate: -50% -50%;
    border-radius: 50%;
    scale: 0;
}
.prp-checkbox:checked:before {
    scale: 1;
}
.checkbox-field[data-v-0c36c412] {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.checkbox-field .content[data-v-0c36c412] {
    width: 100%;
    display: flex;
    gap: 1rem;
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
    align-items: center;
}
.checkbox-field .content.disabled[data-v-0c36c412] {
    color: hsla(0, 0%, 100%, 0.5);
}
.dropdown-wrapper[data-v-6752c2a0] {
    position: relative;
    --radius: 0.5rem;
    --padding: 0.5rem 1rem;
}
.dropdown-wrapper .dropdown-header[data-v-6752c2a0] {
    width: 100%;
    height: 100%;
    background: var(--dropdown-header-bg-color, #0b1203);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: border-radius 0.1s ease, border 0s;
    gap: 1rem;
    border-radius: var(--radius);
    padding: var(--padding);
    font-size: 1rem;
    min-width: 8rem;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    color: var(--dropdown-text-color, inherit);
}
.dropdown-wrapper .dropdown-header.placeholder[data-v-6752c2a0] {
    color: hsla(0, 0%, 100%, 0.35);
}
.dropdown-wrapper .dropdown-header.noOpen[data-v-6752c2a0] {
    cursor: unset;
}
.dropdown-wrapper .dropdown-header.disabled[data-v-6752c2a0] {
    color: hsla(0, 0%, 100%, 0.5);
}
.dropdown-wrapper .dropdown-header.big[data-v-6752c2a0] {
    padding: 0.75rem 1rem;
}
.dropdown-wrapper .dropdown-header[data-v-6752c2a0]:not(.dropdownOpen) {
    transition-delay: 85ms;
}
.dropdown-wrapper .dropdown-header.dropdownOpen[data-v-6752c2a0] {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-color: transparent !important;
}
.dropdown-wrapper .dropdown-header svg[data-v-6752c2a0] {
    color: #fff;
    height: 1rem;
    transition: transform 0.1s ease;
}
.dropdown-wrapper .dropdown-header svg.open[data-v-6752c2a0] {
    transform: rotate(180deg);
}
.dropdown-wrapper .dropdown-header .title[data-v-6752c2a0] {
    flex: 1;
}
.dropdown-wrapper .dropdown-content[data-v-6752c2a0] {
    position: absolute;
    top: calc(100% - 2px);
    left: 0;
    width: 100%;
    background: var(--Black, var(--black));
    border-bottom-left-radius: var(--radius);
    border-bottom-right-radius: var(--radius);
    background: var(--dropdown-content-bg-color, #0b1203);
    color: var(--dropdown-text-color, inherit);
    box-shadow: 0 2px 0.5rem 0 rgba(135, 218, 33, 0.1);
    padding: 0 0.5rem 0.375rem;
    max-height: 10rem;
    overflow: auto;
    display: flex;
    flex-direction: column;
    z-index: 2;
}
.dropdown-wrapper .dropdown-content[data-v-6752c2a0] span {
    padding: 0.5rem 0;
    cursor: pointer;
}
.dropdown-wrapper .dropdown-content[data-v-6752c2a0] span:not(:last-child) {
    border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
}
.fade-enter-active[data-v-6752c2a0],
.fade-leave-active[data-v-6752c2a0] {
    transition: transform 0.1s ease;
    transform-origin: top center;
}
.fade-enter-from[data-v-6752c2a0],
.fade-leave-to[data-v-6752c2a0] {
    transform: scaleY(0) !important;
}
.item[data-v-2ff94f32] {
    padding: 0.5rem 0.5rem;
    cursor: pointer;
    border-top: 1px solid hsla(0, 0%, 100%, 0.05);
    font-weight: 400;
}
.select-field[data-v-30ceec8c] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.select-field .label[data-v-30ceec8c] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
}
.select-field .description[data-v-30ceec8c] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.select-field .search[data-v-30ceec8c] {
    width: 100%;
    height: 100%;
    padding: 0;
    border: none;
    background-color: transparent;
    color: #fff;
    font-family: inherit;
    font-size: 1rem;
}
.select-field .search[data-v-30ceec8c]:focus {
    outline: none;
}
.multi-select-item[data-v-00f2276d] {
    padding: 0.25rem 0.25rem;
    cursor: pointer;
    border-radius: 0.25rem;
    background: rgba(135, 218, 33, 0.25);
    color: hsla(0, 0%, 100%, 0.875);
}
.select-field[data-v-21554410] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.select-field .label[data-v-21554410] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
}
.select-field .description[data-v-21554410] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.select-field .items[data-v-21554410] {
    display: flex;
    gap: 0.25rem;
    flex-flow: row wrap;
}
.prp-slider-wrapper[data-v-1ddab99c] {
    position: relative;
    overflow: visible;
    height: -moz-fit-content;
    height: fit-content;
}
.prp-slider-wrapper.disabled[data-v-1ddab99c] {
    opacity: 0.4;
}
.prp-slider[data-v-1ddab99c] {
    width: 100%;
    height: 0.25rem;
    border: none;
    border-radius: 2rem;
    -webkit-appearance: none;
}
.prp-slider[data-v-1ddab99c]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    -webkit-transition: filter 0.1s ease;
    transition: filter 0.1s ease;
    background: #fff;
}
.label[data-v-1ddab99c] {
    position: absolute;
    top: 0;
    left: calc(var(--left) * 100% - var(--left) * 2 * 0.375rem + 0.375rem);
    translate: -50% -100%;
    border-radius: 0.3125rem;
    background: var(--green-active, #87da21);
    color: var(--Black, #121212);
    display: none;
    padding: 0.25rem 0.375rem;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    width: 2.5rem;
    line-height: 100%;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 700;
}
.label.show[data-v-1ddab99c] {
    display: flex;
}
.label .triangle[data-v-1ddab99c] {
    position: absolute;
    bottom: 0;
    left: 50%;
    translate: -50% 80%;
}
.input-field[data-v-b79627a8] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.input-field.disabled[data-v-b79627a8] {
    pointer-events: none;
    filter: contrast(0.8);
}
.input-field .label[data-v-b79627a8] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
}
.input-field .description[data-v-b79627a8] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.input-field .slider[data-v-b79627a8] {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: #0b1203;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    height: 2.5rem;
}
.input-field .slider .prp-slider-wrapper[data-v-b79627a8] {
    flex: 1;
    margin-bottom: 0.5rem;
}
.date-field[data-v-448c9efa] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.date-field[data-v-448c9efa] input {
    text-align: center;
}
.date-field .label[data-v-448c9efa] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
}
.date-field .description[data-v-448c9efa] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.grid[data-v-448c9efa] {
    display: flex;
    gap: 1.5em;
    flex-wrap: wrap;
    justify-content: center;
}
.date[data-v-448c9efa] {
    width: 100%;
    margin-top: 1rem;
}
.date-range-field[data-v-60268cfc] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.date-range-field .label[data-v-60268cfc] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
}
.date-range-field .description[data-v-60268cfc] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.grid[data-v-60268cfc] {
    display: flex;
    gap: 1.5em;
    flex-wrap: wrap;
    justify-content: center;
}
.date[data-v-60268cfc] {
    width: 100%;
    margin-top: 1rem;
}
.time-field[data-v-16c83242] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.time-field .label[data-v-16c83242] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
}
.time-field .description[data-v-16c83242] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.color-field[data-v-8970c0da] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.color-field .label[data-v-8970c0da] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
}
.color-field .description[data-v-8970c0da] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
.color-field .color-picker[data-v-8970c0da] {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: #0b1203;
    padding: 1rem 1rem;
    border-radius: 0.5rem;
    min-height: 2.5rem;
}
.input-dialog[data-v-a0041cf2] {
    --accent: 1.5rem;
    --accentWidth: 2px;
    --accentColor: #87da21;
    font-family: Geist;
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    translate: -50% -50%;
    padding: 1.5rem 0;
    border-radius: 0.375rem;
    background: rgba(26, 31, 20, 0.75);
    width: 25rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}
.input-dialog.side[data-v-a0041cf2] {
    right: 2.5rem;
    left: unset;
    transform-origin: right center;
    translate: 0 -50%;
}
.input-dialog[data-v-a0041cf2] img {
    max-width: 100%;
    max-height: 100%;
}
.input-dialog .buttons[data-v-a0041cf2] {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}
.input-dialog .content[data-v-a0041cf2] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    width: 100%;
    max-height: 35rem;
    overflow: auto;
    overflow-x: hidden;
    padding: 0 2.5rem 0 1.5rem;
    margin-right: -1.5rem;
}
.input-dialog.visibleOverflow .content[data-v-a0041cf2] {
    overflow: visible;
}
.list-menu-item[data-v-e4978cb6] {
    display: flex;
    gap: 1rem;
    padding: var(--gap-gap-3, 0.75rem);
    border-radius: 0.25rem;
    border: 1.5px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: rgba(26, 31, 20, 0.75);
    transition: border 0.2s ease, background 0.2s ease;
}
.list-menu-item.disabled[data-v-e4978cb6] {
    pointer-events: none;
}
.list-menu-item.disabled .actions .action svg[data-v-e4978cb6],
.list-menu-item.disabled .description[data-v-e4978cb6],
.list-menu-item.disabled .heading[data-v-e4978cb6] {
    color: hsla(0, 0%, 100%, 0.5);
}
.list-menu-item.button[data-v-e4978cb6] {
    cursor: pointer;
}
.list-menu-item.button[data-v-e4978cb6]:hover {
    border: 1.5px solid var(--Brand-Brand, #87da21);
    background: rgba(58, 86, 24, 0.8);
}
.list-menu-item .col[data-v-e4978cb6] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}
.list-menu-item .heading[data-v-e4978cb6] {
    color: #fff;
    font-family: Oswald;
    font-size: 1rem;
}
.list-menu-item .description[data-v-e4978cb6] {
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 400;
}
.list-menu-item .actions[data-v-e4978cb6] {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-right: 1rem;
}
.list-menu-item .actions .action[data-v-e4978cb6] {
    cursor: pointer;
    transition: color 0.2s ease;
}
.list-menu-item .actions .action[data-v-e4978cb6]:hover {
    color: #fff;
}
.list-menu-item .actions .action.noClick[data-v-e4978cb6] {
    cursor: unset;
}
.list-menu-item .actions .action svg[data-v-e4978cb6] {
    color: #fff;
}
.list-menu-header[data-v-4a396d25] {
    width: 100%;
    min-width: 16rem;
    max-width: 24rem;
    display: flex;
    gap: 0.5rem;
    overflow: hidden;
    padding-right: 0.75rem;
}
.list-menu-header.isScrollbar[data-v-4a396d25] {
    padding-right: 1rem;
}
.list-menu-header .content[data-v-4a396d25] {
    padding: var(--gap-gap-2, 0.5rem) var(--gap-gap-3, 0.75rem);
    font-size: 1.25rem;
    color: #fff;
    flex: 1;
    text-align: center;
    text-transform: uppercase;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.list-menu-header .field[data-v-4a396d25] {
    border-radius: 0.25rem;
    border: 1.5px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: rgba(26, 31, 20, 0.75);
    flex-shrink: 0;
}
.list-menu-header .sq-button[data-v-4a396d25] {
    width: 3rem;
    height: 3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: border 0.2s ease, background 0.2s ease;
}
.list-menu-header .sq-button[data-v-4a396d25]:hover {
    border: 1.5px solid var(--Brand-Brand, #87da21);
    background: rgba(58, 86, 24, 0.8);
}
.list-menu-header .sq-button svg[data-v-4a396d25] {
    width: 1.5rem;
    height: 1.5rem;
    stroke-width: 2;
    color: #fff;
}
.list-menu-item[data-v-006cdf20] {
    display: flex;
    gap: 1rem;
    padding: var(--gap-gap-3, 0.75rem);
    border-radius: 0.25rem;
    border: 1.5px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: rgba(26, 31, 20, 0.75);
    transition: border 0.2s ease, background 0.2s ease;
    position: relative;
}
.list-menu-item.hovered[data-v-006cdf20] {
    border: 1.5px solid var(--Brand-Brand, #87da21);
    background: rgba(58, 86, 24, 0.8);
}
.list-menu-item.disabled[data-v-006cdf20] {
    pointer-events: none;
    filter: opacity(0.5) grayscale(0.5);
}
.list-menu-item .col[data-v-006cdf20] {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}
.list-menu-item .row[data-v-006cdf20] {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.list-menu-item .row[data-v-006cdf20]:not(:first-child) {
    margin-top: 1rem;
}
.list-menu-item .heading[data-v-006cdf20] {
    color: #fff;
    font-family: Oswald;
    font-size: 1rem;
}
.list-menu-item .description[data-v-006cdf20] {
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 400;
}
.list-menu-item .chevrons[data-v-006cdf20] {
    color: #fff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.quick-left-enter-active[data-v-006cdf20],
.quick-left-leave-active[data-v-006cdf20],
.quick-left-move[data-v-006cdf20],
.quick-right-enter-active[data-v-006cdf20],
.quick-right-leave-active[data-v-006cdf20],
.quick-right-move[data-v-006cdf20] {
    transition: all 0.05s ease-out;
}
.quick-left-leave-to[data-v-006cdf20],
.quick-right-enter-from[data-v-006cdf20] {
    transform: translateX(-2rem);
    opacity: 0;
}
.quick-left-enter-from[data-v-006cdf20],
.quick-right-leave-to[data-v-006cdf20] {
    transform: translateX(2rem);
    opacity: 0;
}
.list-menu-wrapper[data-v-3571dc74] {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform-origin: right center;
    transform: rotateY(-8deg) translateY(-50%);
    width: 24rem;
    overflow: visible;
}
.list-menu-wrapper .items[data-v-3571dc74] {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 0.5rem,
        #fff calc(100% - 0.5rem),
        transparent
    );
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 25rem;
    overflow: auto;
    padding-right: 0.75rem;
}
.slide-right-fast-enter-active[data-v-3571dc74],
.slide-right-fast-leave-active[data-v-3571dc74],
.slide-right-fast-move[data-v-3571dc74] {
    transition: all 0.15s ease-out;
}
.slide-right-fast-enter-from[data-v-3571dc74],
.slide-right-fast-leave-to[data-v-3571dc74] {
    transform: translateX(100%) rotateY(20deg);
    opacity: 0;
}
.list-menu-item[data-v-1562d47c] {
    display: flex;
    gap: 1rem;
    padding: var(--gap-gap-3, 0.75rem);
    color: #fff;
    border-radius: 0.25rem;
    border: 1.5px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: rgba(26, 31, 20, 0.75);
    transition: border 0.2s ease, background 0.2s ease;
    cursor: pointer;
}
.list-menu-item.disabled[data-v-1562d47c] {
    pointer-events: none;
    filter: opacity(0.5) grayscale(0.5);
}
.list-menu-item svg[data-v-1562d47c] {
    color: hsla(0, 0%, 100%, 0.75);
    transition: color 0.2s ease;
}
.list-menu-item.hover[data-v-1562d47c],
.list-menu-item[data-v-1562d47c]:hover {
    border: 1.5px solid var(--Brand-Brand, #87da21);
    background: rgba(58, 86, 24, 0.8);
}
.list-menu-item.hover svg[data-v-1562d47c],
.list-menu-item:hover svg[data-v-1562d47c] {
    color: #fff;
}
.list-menu-item .row[data-v-1562d47c] {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.list-menu-item .row[data-v-1562d47c]:not(:first-child) {
    margin-top: 1rem;
}
.list-menu-item .col[data-v-1562d47c] {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}
.list-menu-item .heading[data-v-1562d47c] {
    color: #fff;
    font-family: Oswald;
    font-size: 1rem;
}
.list-menu-item .description[data-v-1562d47c] {
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 400;
}
.ox-context[data-v-ee20fb6e] {
    position: absolute;
    left: -0.5rem;
    top: 0;
    translate: -100% 0;
    color: #fff;
    min-width: 15rem;
    max-width: 20rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: var(--gap-gap-3, 0.75rem);
    border-radius: 0.25rem;
    border: 1.5px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: rgba(26, 31, 20, 0.75);
    z-index: -1;
}
.ox-context .col[data-v-ee20fb6e] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.ox-context .row[data-v-ee20fb6e] {
    width: 100%;
    gap: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.ox-context .metadata-entry[data-v-ee20fb6e] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.ox-context .metadata-entry .label[data-v-ee20fb6e] {
    color: #fff;
    font-family: Oswald;
    font-size: 1rem;
}
.ox-context .metadata-entry .value[data-v-ee20fb6e] {
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 400;
}
.ox-context .image[data-v-ee20fb6e] {
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: 8rem;
    overflow: hidden;
    border-radius: 0.3125rem;
}
.ox-context .image img[data-v-ee20fb6e] {
    max-width: 100%;
    max-height: 100%;
}
.list-menu-wrapper[data-v-15a38bc4] {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform-origin: right center;
    transform: rotateY(-8deg) translateY(-50%);
    width: 24rem;
    overflow: visible;
}
.list-menu-wrapper .items[data-v-15a38bc4] {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 0.5rem,
        #fff calc(100% - 0.5rem),
        transparent
    );
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 25rem;
    overflow: auto;
    padding-right: 0.75rem;
}
.slide-right-fast-enter-active[data-v-15a38bc4],
.slide-right-fast-leave-active[data-v-15a38bc4],
.slide-right-fast-move[data-v-15a38bc4] {
    transition: all 0.15s ease-out;
}
.slide-right-fast-enter-from[data-v-15a38bc4],
.slide-right-fast-leave-to[data-v-15a38bc4] {
    transform: translateX(100%) rotateY(20deg);
    opacity: 0;
}
.text-ui-wrapper[data-v-bf45a24a] {
    position: absolute;
    padding: 0.75rem 1rem;
    color: #fff;
    --accent: 1rem;
    --accentWidth: 2px;
    --accentColor: #87da21;
    --bg: #39620680;
    --border: #64744d;
    background: var(--bg);
    align-items: center;
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    display: flex;
    gap: 1rem;
}
.text-ui-wrapper.right-center[data-v-bf45a24a] {
    top: 50%;
    right: 2rem;
    translate: 0 -50%;
    transform: rotateY(-8deg);
}
.text-ui-wrapper.left-center[data-v-bf45a24a] {
    top: 50%;
    left: 2rem;
    translate: 0 -50%;
    transform: rotateY(8deg);
}
.text-ui-wrapper.top-center[data-v-bf45a24a] {
    top: 2.5rem;
    left: 50%;
    translate: -50% 0;
    transform: rotateX(-8deg);
}
.text-ui-wrapper.top-center.topBarVisible[data-v-bf45a24a] {
    top: 6rem;
}
.text-ui-wrapper.bottom-center[data-v-bf45a24a] {
    bottom: 2.5rem;
    left: 50%;
    translate: -50% 0;
    transform: rotateX(8deg);
}
.slide-text-ui-enter-active[data-v-bf45a24a],
.slide-text-ui-leave-active[data-v-bf45a24a],
.slide-text-ui-move[data-v-bf45a24a] {
    transition: all 0.25s ease-out;
}
.slide-text-ui-enter-from[data-v-bf45a24a],
.slide-text-ui-leave-to[data-v-bf45a24a] {
    opacity: 0;
}
.slide-text-ui-enter-from.right-center[data-v-bf45a24a],
.slide-text-ui-leave-to.right-center[data-v-bf45a24a] {
    transform: translateX(100%) rotateY(20deg);
}
.slide-text-ui-enter-from.left-center[data-v-bf45a24a],
.slide-text-ui-leave-to.left-center[data-v-bf45a24a] {
    transform: translateX(-100%) rotateY(-20deg);
}
.slide-text-ui-enter-from.top-center[data-v-bf45a24a],
.slide-text-ui-leave-to.top-center[data-v-bf45a24a] {
    transform: translateY(-100%) rotateX(-20deg);
}
.slide-text-ui-enter-from.bottom-center[data-v-bf45a24a],
.slide-text-ui-leave-to.bottom-center[data-v-bf45a24a] {
    transform: translateY(100%) rotateX(20deg);
}
.wrapper[data-v-25876e06] {
    perspective: var(--perspective);
}
.list-menu-wrapper[data-v-04c57084] {
    transform: rotateY(-8deg);
    width: 24rem;
}
.list-menu-wrapper .items[data-v-04c57084] {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 0.5rem,
        #fff calc(100% - 0.5rem),
        transparent
    );
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 25rem;
    overflow: auto;
    padding-right: 0.75rem;
}
.slide-right-fast-enter-active[data-v-04c57084],
.slide-right-fast-leave-active[data-v-04c57084],
.slide-right-fast-move[data-v-04c57084] {
    transition: all 0.15s ease-out;
}
.slide-right-fast-enter-from[data-v-04c57084],
.slide-right-fast-leave-to[data-v-04c57084] {
    transform: translateX(100%) rotateY(20deg);
    opacity: 0;
}
.wrapper[data-v-a47f0860] {
    perspective: var(--perspective);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 2.5rem;
}
.inspect-wrapper[data-v-e5a19472] {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.85)),
        linear-gradient(180deg, rgba(0, 0, 0, 0.5), transparent);
    position: relative;
    z-index: 0;
}
.inspect-wrapper .title[data-v-e5a19472] {
    font-size: 3rem;
    font-family: Oswald;
    font-weight: 400;
    color: #fff;
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
    text-transform: uppercase;
}
.inspect-wrapper canvas[data-v-e5a19472] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.inspect-wrapper .actions[data-v-e5a19472] {
    position: absolute;
    bottom: 2rem;
    left: 0;
    right: 0;
    margin: auto;
    display: flex;
    gap: 2rem;
    align-items: center;
    justify-content: center;
    z-index: 1;
}
.inspect-wrapper .action[data-v-e5a19472] {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.inspect-wrapper .action span[data-v-e5a19472] {
    font-size: 1rem;
    font-weight: 400;
    color: #fff;
    font-family: Oswald;
    text-transform: uppercase;
}
.inspect-wrapper .action button[data-v-e5a19472] {
    display: flex;
    padding: 0.625rem;
    justify-content: center;
    align-items: center;
    border-radius: 0.375rem;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: hsla(0, 0%, 100%, 0.1);
    font-size: 0.875rem;
    font-weight: 400;
    color: #fff;
    font-family: Oswald;
    line-height: 100%;
    height: 2rem;
    min-width: 2rem;
}
.circular-progress[data-v-1b9a98d0] {
    width: 5rem;
    height: 5rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
}
.circular-progress canvas[data-v-1b9a98d0] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.timer[data-v-081420b4] {
    --accent: 0.5rem;
    --accentColor: #fff;
    --accentWidth: 2px;
    padding: 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #a9a0a0;
    background: rgba(40, 40, 40, 0.25);
    font-family: Oswald;
    font-size: 1.5rem;
    text-transform: uppercase;
    line-height: 100%;
    width: 5rem;
}
.hold-button[data-v-020c3727],
.timer[data-v-081420b4] {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
}
.hold-button[data-v-020c3727] {
    position: relative;
    padding: 0.5rem;
    border-radius: 0.25rem;
    background: hsla(0, 0%, 100%, 0.15);
    overflow: hidden;
    border: 1px solid transparent;
    font-size: 1rem;
    font-family: Geist;
    line-height: 90%;
    color: #fff;
    height: 2rem;
    min-width: 2rem;
    isolation: isolate;
}
.hold-button .mod[data-v-020c3727] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: hsla(0, 0%, 100%, 0.25);
    transform-origin: left center;
    scale: var(--w) 1;
    z-index: -1;
}
.unconscious[data-v-4128f189] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    color: #fff;
}
.unconscious h2[data-v-4128f189] {
    margin: 0;
    font-family: Oswald;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 400;
    text-transform: uppercase;
}
.unconscious .blood[data-v-4128f189] {
    position: absolute;
    bottom: -20rem;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 55rem;
    height: 41rem;
    background: radial-gradient(circle, #ef0000 0, transparent 45%);
    z-index: -1;
    animation: breathe 5s infinite;
}
.unconscious .info-text[data-v-4128f189] {
    color: hsla(0, 0%, 100%, 0.65);
    font-size: 0.875rem;
    font-family: Geist;
    font-weight: 400;
    line-height: 150%;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.knocked-down[data-v-6756227e] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    color: #fff;
}
.knocked-down h2[data-v-6756227e] {
    margin: 0;
    font-family: Oswald;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 400;
    text-transform: uppercase;
}
.knocked-down .info-text[data-v-6756227e] {
    color: hsla(0, 0%, 100%, 0.65);
    font-size: 0.875rem;
    font-family: Geist;
    font-weight: 400;
    line-height: 150%;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.healing[data-v-077a287f] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    color: #fff;
}
.healing h2[data-v-077a287f] {
    margin: 0;
    font-family: Oswald;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 400;
    text-transform: uppercase;
}
.healing .info-text[data-v-077a287f] {
    color: hsla(0, 0%, 100%, 0.65);
    font-size: 0.875rem;
    font-family: Geist;
    font-weight: 400;
    line-height: 150%;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.healing .circ-prog[data-v-077a287f] {
    position: relative;
    display: inline-flex;
}
.healing .red-btn[data-v-077a287f] {
    border: 1px solid red;
    background: rgba(255, 0, 0, 0.15);
}
.healing .red-btn[data-v-077a287f] .mod {
    background: rgba(255, 0, 0, 0.35);
}
.ko-screen[data-v-20fdcc95] {
    perspective: var(--perspective);
}
.ko-screen .wrapper-inner[data-v-20fdcc95],
.ko-screen[data-v-20fdcc95] {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: 2.5rem;
    pointer-events: none;
}
.ko-screen .wrapper-inner[data-v-20fdcc95] {
    transform: rotateX(-5deg);
}
.ko-screen .fade-out[data-v-20fdcc95] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0;
    transition: opacity 0.5s ease;
}
.ko-screen .fade-out.visible[data-v-20fdcc95] {
    opacity: 1;
}
.animation-categories[data-v-15ca199b] {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: -1rem;
    padding: 1rem 3rem 1rem 1px;
    margin-right: -1.5rem;
    margin-bottom: -1rem;
    direction: ltr;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.animation-categories[data-v-15ca199b]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.animation-categories .animation-category[data-v-15ca199b] {
    display: flex;
    width: 3rem;
    height: 3rem;
    padding: 0.75rem;
    justify-content: center;
    align-items: center;
    direction: ltr;
    --imBorderWidth: 2px;
    --imBorderBg: linear-gradient(
        180deg,
        transparent 0%,
        hsla(0, 0%, 100%, 0.25)
    );
    border-radius: 0.5rem;
    border: 2px solid transparent;
    background: var(--black);
    transition: background 0.2s ease, border 0.2s ease;
    cursor: pointer;
}
.animation-categories .animation-category svg[data-v-15ca199b] {
    width: 1.5rem;
    height: 1.5rem;
}
.animation-categories .animation-category img[data-v-15ca199b] {
    width: 2rem;
    height: 2rem;
    -o-object-fit: cover;
    object-fit: cover;
}
.animation-categories .animation-category[data-v-15ca199b]:hover:not(.active) {
    border-radius: 0.5rem;
    border: 2px solid var(--Bright-Green, rgba(var(--green-active-rgb, 0.3)));
    background: rgba(var(--green-active-rgb), 0.1);
}
.animation-categories
    .animation-category[data-v-15ca199b]:hover:not(.active):before {
    opacity: 0;
}
.animation-categories .animation-category.active[data-v-15ca199b] {
    border-radius: 0.5rem;
    border: 2px solid var(--green-active);
    background: rgba(var(--green-active-rgb), 0.2);
}
.animation-categories .animation-category.active[data-v-15ca199b]:before {
    opacity: 0;
}
.modal-wrapper[data-v-247ef24a] {
    perspective: var(--perspective);
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
}
.modal-wrapper .modal[data-v-247ef24a] {
    --accent: 1.5rem;
    --accentWidth: 2px;
    --accentColor: var(--green-active);
    padding: var(--L, 1.5rem) var(--L, 1.5rem) var(--XL, 2rem) var(--L, 1.5rem);
    border-radius: 0.375rem;
    background: var(--modal-bg);
}
.modal-wrapper .modal[data-v-247ef24a] .header {
    font-size: 1.25rem;
    font-weight: 500;
}
.internal-modal-wrapper[data-v-7b7da130] {
    position: fixed;
    width: 1000vw;
    height: 1000vh;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    z-index: 5;
}
.you-sure-modal[data-v-4fd240ae] {
    color: #fff;
}
.you-sure-modal[data-v-4fd240ae] .modal {
    display: flex;
    align-items: center;
    flex-direction: column;
    text-align: center;
    font-size: 1.125rem;
    gap: 1rem;
}
.you-sure-modal .buttons[data-v-4fd240ae] {
    margin-top: 0;
    display: flex;
    gap: 0.75rem;
}
.animation-list-item[data-v-9e955e16] {
    display: flex;
    padding: 0.75rem;
    padding-bottom: 0;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    height: 12rem;
    isolation: isolate;
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(
        180deg,
        transparent 0%,
        hsla(0, 0%, 100%, 0.25)
    );
    border-radius: 0.5rem;
    transition: background 0.1s ease, border 0.2s ease;
    background: var(--anim-bg);
}
.animation-list-item[data-v-9e955e16]:hover {
    --imBorderBg: var(--green-active);
    background: var(--anim-bg-hover);
}
.animation-list-item .row[data-v-9e955e16] {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
}
.animation-list-item .bind-loc[data-v-9e955e16] {
    padding: var(--gap-gap-1, 0.25rem) var(--gap-gap-15, 0.375rem);
    border-radius: var(--XXS, 0.5rem);
    border: 1px solid var(--White-White-5, hsla(0, 0%, 100%, 0.05));
    background: hsla(0, 0%, 100%, 0.15);
    line-height: 100%;
    font-size: 0.75rem;
    cursor: pointer;
}
.animation-list-item .bind-loc[data-v-9e955e16]:hover {
    background: hsla(0, 0%, 100%, 0.25);
}
.animation-list-item .favourite[data-v-9e955e16] {
    cursor: pointer;
    color: hsla(0, 0%, 100%, 0.1);
}
.animation-list-item .favourite.active[data-v-9e955e16] {
    color: #ffe500;
}
.animation-list-item .image[data-v-9e955e16] {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}
.animation-list-item .image img[data-v-9e955e16],
.animation-list-item .image video[data-v-9e955e16] {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 1px;
}
.animation-list-item .image svg[data-v-9e955e16] {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    font-size: 4rem;
    color: hsla(0, 0%, 100%, 0.5);
}
.animation-list-item .label[data-v-9e955e16] {
    font-family: Oswald;
    font-size: 0.875rem;
    margin-top: auto;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding: 5rem 0.75rem 0.75rem;
    background: radial-gradient(
        circle at 50% calc(100% - 0.75rem),
        rgba(0, 0, 0, 0.25) 0,
        transparent 50%
    );
    cursor: pointer;
}
.animation-list-item .label .bind-loc[data-v-9e955e16] {
    font-size: 0.875rem;
    transition: all 0.2s ease;
}
.animation-list-item .label:not(:hover) .bind-loc[data-v-9e955e16] {
    max-height: 0;
    overflow: hidden;
    padding: 0;
    border: 0;
}
.animation-list-item .label p[data-v-9e955e16]:not(.isLabel) {
    text-transform: lowercase;
}
.keybind-modal[data-v-ed6d9306] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}
.keybind-modal h2[data-v-ed6d9306] {
    font-weight: 400;
}
.keybind-modal .field[data-v-ed6d9306] {
    width: 20rem;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--input-bg);
    font-size: 1.5rem;
    text-align: center;
}
.keybind-modal .field.waiting[data-v-ed6d9306] {
    color: hsla(0, 0%, 100%, 0.5);
}
.keybind-modal .description[data-v-ed6d9306] {
    text-align: center;
    font-family: Geist;
    font-size: 1rem;
    color: hsla(0, 0%, 100%, 0.75);
    line-height: 150%;
}
.keybind-modal .description.error[data-v-ed6d9306] {
    color: red;
}
.keybind-modal .description.warning[data-v-ed6d9306] {
    color: #f96;
}
.keybind-modal .buttons[data-v-ed6d9306] {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}
.animation-list[data-v-47c71623] {
    display: grid;
    grid-gap: 0.75rem;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    grid-template-rows: min-content;
    max-height: 100%;
    overflow: auto;
    margin-right: -2.5rem;
    margin-top: -1rem;
    padding: 1rem 2.5rem 1rem 1px;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.animation-list[data-v-47c71623]::-webkit-scrollbar {
    display: none;
}
.animation-list[data-v-47c71623]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.animation-list.noGrid[data-v-47c71623] {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Oswald;
    font-size: 2rem;
}
.animation-menu[data-v-239ef06f] {
    position: absolute;
    right: 2.5rem;
    top: 50%;
    translate: 0 -50%;
    transform-origin: right center;
    scale: 0.9;
    width: 58rem;
    height: 47rem;
    border-radius: 0.5rem;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    color: #fff;
    --green-bg: #425c2c;
    --dark-green-bg: rgba(20, 31, 7, 0.75);
    --black: rgba(0, 0, 0, 0.5);
    --black-hover: rgba(0, 0, 0, 0.75);
    --anim-bg: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0) 2.22%,
            rgba(176, 255, 40, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    --anim-bg-hover: linear-gradient(
            0deg,
            rgba(158, 255, 0, 0.2),
            rgba(158, 255, 0, 0.2)
        ),
        rgba(36, 48, 15, 0.75);
    background: var(--dark-green-bg);
}
.animation-menu.pink[data-v-239ef06f] {
    --green-active: #e840a6;
    --bright-green: #fc70c5;
    --green-active-rgb: 232, 64, 166;
    --green-bg: #442a36;
    --dark-green-bg: linear-gradient(
            0deg,
            rgba(255, 140, 196, 0.3),
            rgba(255, 140, 196, 0.3)
        ),
        rgba(0, 0, 0, 0.85);
    --button-bg: rgba(252, 112, 197, 0.8);
    --button-bg-hover: rgba(252, 112, 197, 0.4);
    --black: rgba(0, 0, 0, 0.5);
    --black-hover: rgba(0, 0, 0, 0.75);
    --anim-bg: radial-gradient(
            61.41% 69.11% at 51.27% 50.99%,
            rgba(20, 26, 9, 0) 0%,
            rgba(255, 50, 175, 0.25) 100%
        ),
        rgba(0, 0, 0, 0.55);
    --anim-bg-hover: radial-gradient(
            61.41% 69.11% at 51.27% 50.99%,
            rgba(20, 26, 9, 0) 0%,
            rgba(255, 50, 175, 0.25) 100%
        ),
        rgba(0, 0, 0, 0.55);
    --modal-bg: #401d32;
    --input-bg: #341828;
}
.animation-menu h2[data-v-239ef06f] {
    font-size: 2rem;
    font-weight: 400;
    line-height: normal;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}
.animation-menu h2 .col[data-v-239ef06f] {
    display: flex;
    flex-direction: column;
    font-family: Oswald;
    font-size: 1.25rem;
}
.animation-menu h2 .col .dark[data-v-239ef06f] {
    font-family: Oswald;
    font-size: 0.625rem;
    color: hsla(0, 0%, 100%, 0.5);
}
.animation-menu .prp-input[data-v-239ef06f] {
    font-family: Geist;
}
.animation-menu .view[data-v-239ef06f] {
    flex: 1;
    min-height: 0;
    display: grid;
    grid-template-columns: 4.5rem 1fr;
    grid-template-rows: 100%;
    grid-gap: 1rem;
}
.slide-up-enter-active[data-v-239ef06f],
.slide-up-leave-active[data-v-239ef06f] {
    transition: all 0.1s ease-out;
}
.slide-up-enter-from[data-v-239ef06f] {
    opacity: 0;
    transform: translateY(0.25rem) rotateX(-20deg);
}
.slide-up-leave-to[data-v-239ef06f] {
    opacity: 0;
    transform: translateY(-0.25rem) rotateX(20deg);
}
.wrapper[data-v-6dd607c7] {
    perspective: var(--perspective);
}
.slanted-button[data-v-42a57d26] {
    border: 2px solid #9eff00;
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    transform: skew(-6deg);
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle, #9eff00 0, #508000 80%);
    border-radius: 0.125rem;
    cursor: pointer;
    isolation: isolate;
    position: relative;
}
.slanted-button.disabled[data-v-42a57d26] {
    filter: grayscale(0.5);
    pointer-events: none;
}
.slanted-button[data-v-42a57d26]:before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    width: inherit;
    height: inherit;
    background: #9eff00;
    opacity: 0;
    transition: opacity 0.2s ease;
}
.slanted-button[data-v-42a57d26]:hover:before {
    opacity: 1;
}
.slanted-button[data-v-42a57d26] > * {
    transform: skew(6deg);
}
.character-card-wrapper[data-v-ee4b7ea2] {
    padding: 0 1.25rem;
}
.character-card[data-v-ee4b7ea2] {
    width: 100%;
    height: -moz-fit-content;
    height: fit-content;
    display: flex;
    align-items: center;
    cursor: pointer;
}
.character-card.disabled[data-v-ee4b7ea2] {
    pointer-events: none;
    filter: grayscale(0.9);
}
.character-card:hover .slanted-image[data-v-ee4b7ea2]:before {
    opacity: 1;
}
.character-card:hover .slanted-image .image-wrapper img[data-v-ee4b7ea2] {
    transform: skew(8deg);
    filter: brightness(1.2);
}
.character-card:hover .slanted-image .trash-button[data-v-ee4b7ea2] {
    opacity: 1;
}
.character-card:hover .character-info[data-v-ee4b7ea2] {
    border: 2px solid #9eff00;
    background: #253b0b;
}
.character-card .character-info[data-v-ee4b7ea2] {
    width: 80%;
    min-width: 0;
    margin-left: -1rem;
    border-radius: 0.5rem;
    border: var(--icon-stroke, 2px) solid
        var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: #141f07;
    font-family: Geist;
    font-size: 1.125rem;
    line-height: 100%;
    color: #fff;
    font-weight: 500;
    padding: 0.875rem 1rem 0.875rem 2.5rem;
    transition: border 0.2s ease, background 0.2s ease;
    transform: skew(-8deg);
    z-index: -1;
}
.character-card .character-info .content[data-v-ee4b7ea2] {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 0.6125rem;
    transform: skew(8deg);
}
.character-card .character-info.data[data-v-ee4b7ea2] {
    height: 8rem;
}
.character-card .character-info .row[data-v-ee4b7ea2] {
    max-width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}
.character-card .character-info .row span[data-v-ee4b7ea2] {
    color: hsla(0, 0%, 100%, 0.75);
    font-family: Geist;
    font-size: 0.875rem;
}
.character-card .character-info .row p[data-v-ee4b7ea2] {
    font-family: Geist;
    font-size: 1.125rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.character-card .slanted-image[data-v-ee4b7ea2] {
    height: 8rem;
    width: 8rem;
    border: 2px solid #9eff00;
    background: radial-gradient(circle, #9eff00 20%, #508000 80%);
    transform: skew(-8deg);
    isolation: isolate;
    position: relative;
    border-radius: 0.3125rem;
    display: flex;
    overflow: hidden;
    flex-shrink: 0;
}
.character-card .slanted-image.data[data-v-ee4b7ea2] {
    height: 8rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.character-card .slanted-image[data-v-ee4b7ea2]:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #9eff00 40%, #508000 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
}
.character-card .slanted-image .plus[data-v-ee4b7ea2] {
    width: 3rem;
    height: 3rem;
    stroke-width: 0.125rem;
    color: rgba(0, 0, 0, 0.2);
    margin: auto;
    transform: skew(8deg);
}
.character-card .slanted-image .trash-button[data-v-ee4b7ea2] {
    position: absolute;
    border-radius: 0.375rem;
    border: 1.5px solid var(--Red-Red, red);
    background: var(--Red-Red-50, rgba(255, 0, 0, 0.5));
    width: 2rem;
    height: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    transform: skew(8deg);
    z-index: 5;
    top: 0.5rem;
    right: 0.5rem;
    opacity: 0;
    transition: background 0.2s ease;
    cursor: pointer;
}
.character-card .slanted-image .trash-button[data-v-ee4b7ea2]:hover {
    background: var(--Red-Red-50, rgba(255, 0, 0, 0.75));
}
.character-card .slanted-image .image-wrapper[data-v-ee4b7ea2] {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0.125rem;
    overflow: hidden;
    -webkit-mask: linear-gradient(
        90deg,
        transparent 0,
        #fff 2rem,
        #fff calc(100% - 2rem),
        transparent
    );
}
.character-card .slanted-image .image-wrapper img[data-v-ee4b7ea2] {
    position: absolute;
    width: 100%;
    height: 100%;
    transform: skew(8deg);
    transition: filter 0.2s ease;
}
.char-selector[data-v-371b1e2e] {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 2.5rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    justify-content: center;
    padding: 0 2.5rem;
    opacity: 0 !important;
    transition: opacity 0.2s ease;
    pointer-events: none;
}
.char-selector.ready[data-v-371b1e2e] {
    opacity: 1 !important;
    pointer-events: auto;
}
.char-selector .carousel[data-v-371b1e2e] {
    flex: 1;
    display: flex;
    overflow: auto;
    max-width: 120rem;
    -webkit-mask: linear-gradient(
        90deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.char-selector .carousel[data-v-371b1e2e]::-webkit-scrollbar {
    display: none;
}
.char-selector .carousel .page[data-v-371b1e2e] {
    min-width: 100%;
    display: flex;
}
.char-selector .carousel .page > div[data-v-371b1e2e] {
    width: calc(1 / var(--itemCount) * 100%);
    flex-shrink: 0;
}
.splash-art[data-v-3acba0cb] {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
}
.splash-art img[data-v-3acba0cb] {
    width: 40rem;
}
.splash-art .instruction[data-v-3acba0cb] {
    position: absolute;
    left: 50%;
    bottom: 2.5rem;
    translate: -50% 0;
    text-transform: uppercase;
}
.input-calendar-field[data-v-e17890f2] {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}
.input-calendar-field .label[data-v-e17890f2] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.875));
    font-family: Geist;
    font-size: 0.875rem;
    transition: color 0.1s ease;
}
.input-calendar-field[data-v-e17890f2] .prp-input {
    outline: 1px solid transparent;
    transition: outline-color 0.1s ease;
}
.input-calendar-field:focus-within .label[data-v-e17890f2] {
    color: #87da21;
}
.input-calendar-field[data-v-e17890f2]:focus-within .prp-input {
    outline: 1px solid var(--Brand, #87da21);
}
.input-calendar-field .description[data-v-e17890f2] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
    font-family: Geist;
    font-size: 0.75rem;
}
[data-v-e17890f2] .prp-date-picker {
    --dp-background-color: #1b2016;
    --dp-text-color: #fff;
    --dp-hover-color: #87da21a2;
    --dp-primary-color: #87da21;
    --dp-icon-color: #fff;
    --dp-hover-icon-color: rgba(26, 31, 20, 0.95);
    --dp-hover-text-color: rgba(26, 31, 20, 0.95);
    --dp-border-color: #0b1203;
    --dp-menu-border-color: #0b1203;
    --dp-secondary-color: hsla(0, 0%, 100%, 0.313);
}
[data-v-e17890f2] .prp-date-picker-input {
    --dp-background-color: #0b1203;
    --dp-text-color: #fff;
    --dp-hover-color: #87da21;
    --dp-icon-color: #fff;
    --dp-hover-icon-color: rgba(26, 31, 20, 0.95);
    --dp-hover-text-color: rgba(26, 31, 20, 0.95);
    --dp-border-radius: 0.5rem;
    --dp-border-color: none;
    --dp-font-family: Geist;
}
.character-creator[data-v-32b3a7d2] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    color: #fff;
    font-family: Geist;
}
.character-creator h2[data-v-32b3a7d2] {
    font-size: 1.5rem;
}
.character-creator .cols[data-v-32b3a7d2] {
    display: grid;
    grid-gap: 2rem;
    grid-template-columns: 1fr 1fr;
}
.char-selector-loader[data-v-04f3e3f2],
.character-creator .buttons[data-v-32b3a7d2] {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.char-selector-loader[data-v-04f3e3f2] {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    flex-direction: column;
    color: #fff;
    font-family: Geist;
}
.char-selector-loader h3[data-v-04f3e3f2] {
    text-transform: uppercase;
    font-size: 1.5rem;
}
.black-screen[data-v-186eac76] {
    opacity: 1;
    transition: opacity 0.2s ease;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    background: #000;
}
.black-screen.ready[data-v-186eac76] {
    pointer-events: none;
    opacity: 0;
}
.title[data-v-73b2566b] {
    font-size: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.title .categories[data-v-73b2566b] {
    display: flex;
    gap: 2rem;
    text-transform: uppercase;
}
.title .categories .category[data-v-73b2566b] {
    color: hsla(0, 0%, 100%, 0.5);
    border-bottom: 1px solid transparent;
    cursor: pointer;
}
.title .categories .category.active[data-v-73b2566b] {
    color: #fff;
    border-bottom: 1px solid var(--green-active);
}
.title .close-button[data-v-73b2566b] {
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: rgba(0, 0, 0, 0.5);
    width: 3rem;
    height: 3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background 0.2s ease;
}
.title .close-button[data-v-73b2566b]:hover {
    background: rgba(0, 0, 0, 0.75);
}
.title .close-button svg[data-v-73b2566b] {
    width: var(--icon-size, 1.5rem);
    height: var(--icon-size, 1.5rem);
}
.clothing-menu-category[data-v-546f9a57] {
    width: 100%;
    aspect-ratio: 1;
    border-radius: 0.5rem;
    border: 1px solid transparent;
    background: var(--dark-green-bg);
    transition: background 0.2s ease, border 0.2s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.clothing-menu-category img[data-v-546f9a57],
.clothing-menu-category svg[data-v-546f9a57] {
    transition: all 0.2s ease;
}
.clothing-menu-category svg[data-v-546f9a57] {
    font-size: 1.5rem;
}
.clothing-menu-category.active[data-v-546f9a57] {
    border-radius: 0.5rem;
    border: 1px solid var(--bright-green);
    background: var(--green-bg);
    cursor: unset;
}
.clothing-menu-category.active[data-v-546f9a57]:hover {
    border: 1px solid var(--bright-green, rgba(158, 255, 0, 0.627));
    background: var(--green-bg);
}
.clothing-menu-category[data-v-546f9a57]:hover {
    background: var(--dark-green-bg);
    border: 1px solid hsla(0, 0%, 100%, 0.1);
}
.clothing-menu-category:hover img[data-v-546f9a57],
.clothing-menu-category:hover svg[data-v-546f9a57] {
    opacity: 0.8;
}
.clothing-menu-categories-wrapper[data-v-e61d6780] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.clothing-menu-categories-wrapper h4[data-v-e61d6780] {
    font-size: 1.125rem;
    font-weight: 400;
}
.clothing-menu-categories-wrapper .clothing-menu-categories[data-v-e61d6780] {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    grid-gap: 1rem;
}
.number-wheel[data-v-6831ed15] {
    color: #fff;
    border: 1px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    border-radius: 0.5rem;
    overflow: hidden;
    width: -moz-fit-content;
    width: fit-content;
}
.number-wheel.disabled[data-v-6831ed15] {
    pointer-events: none;
}
.number-wheel .wheel[data-v-6831ed15] {
    display: flex;
    align-items: center;
    background: var(--black);
    height: 100%;
}
.number-wheel .wheel .field[data-v-6831ed15] {
    width: 1.75rem;
    height: 100%;
    padding: 0.75rem 0;
    cursor: pointer;
    transition: background-color 0.1s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 100%;
}
.number-wheel .wheel .field[data-v-6831ed15]:hover {
    background: var(--black-hover);
}
.number-wheel .wheel .field:hover svg[data-v-6831ed15] {
    color: #fff;
}
.number-wheel .wheel .field svg[data-v-6831ed15] {
    color: hsla(0, 0%, 100%, 0.5);
    transition: color 0.1s ease;
    font-size: 0.75rem;
}
.number-wheel .wheel .value[data-v-6831ed15] {
    display: flex;
    width: 2rem;
    height: 100%;
}
.number-wheel .wheel .value input[data-v-6831ed15] {
    background: transparent;
    color: #fff;
    width: 100%;
    height: 100%;
    border: none;
    text-align: center;
    font-weight: 700;
}
.number-wheel .wheel .value input[data-v-6831ed15]:focus {
    outline: none;
}
.number-wheel .wheel .value input[data-v-6831ed15]::-webkit-inner-spin-button,
.number-wheel .wheel .value input[data-v-6831ed15]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    appearance: none;
}
.photo-list-item[data-v-e1437710] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    height: 12rem;
    isolation: isolate;
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(
        180deg,
        transparent 0%,
        hsla(0, 0%, 100%, 0.25)
    );
    border-radius: 0.5rem;
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0) 2.22%,
            rgba(176, 255, 40, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    transition: background 0.1s ease, border 0.2s ease, box-shadow 0.2s ease;
}
.photo-list-item.active[data-v-e1437710],
.photo-list-item[data-v-e1437710]:hover {
    --imBorderBg: #87cd27;
    background: linear-gradient(
            0deg,
            rgba(158, 255, 0, 0.2),
            rgba(158, 255, 0, 0.2)
        ),
        rgba(36, 48, 15, 0.75);
}
.photo-list-item.active.halloween[data-v-e1437710],
.photo-list-item:hover.halloween[data-v-e1437710] {
    --imBorderBg: linear-gradient(
        338.28deg,
        rgba(#f79915, 1),
        rgba(#ff7422, 1) 99.13%
    );
    background: linear-gradient(
            143deg,
            rgba(255, 116, 34, 0.6) 2.22%,
            rgba(247, 153, 21, 0.25) 50%,
            #f79915
        ),
        rgba(0, 0, 0, 0.55);
}
.photo-list-item.pink[data-v-e1437710] {
    --imBorderBg: hsla(0, 0%, 100%, 0.05);
    background: radial-gradient(
            61.41% 69.11% at 51.27% 50.99%,
            rgba(20, 26, 9, 0) 0,
            rgba(252, 112, 197, 0.15) 100%
        ),
        rgba(0, 0, 0, 0.55);
}
.photo-list-item.pink.active[data-v-e1437710],
.photo-list-item.pink[data-v-e1437710]:hover {
    --imBorderBg: #fc70c5;
    background: radial-gradient(
            61.41% 69.11% at 51.27% 50.99%,
            rgba(20, 26, 9, 0) 0,
            rgba(255, 50, 175, 0.25) 100%
        ),
        rgba(0, 0, 0, 0.55);
    box-shadow: 0 4px 24px 0 rgba(252, 112, 197, 0.25);
}
.photo-list-item .image[data-v-e1437710] {
    position: absolute;
    height: calc(100% - 4rem);
    width: 80%;
    top: 1rem;
    left: 50%;
    translate: -50% 0;
    z-index: -1;
}
.photo-list-item .image img[data-v-e1437710],
.photo-list-item .image video[data-v-e1437710] {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
.photo-list-item .image svg[data-v-e1437710] {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    font-size: 4rem;
    color: hsla(0, 0%, 100%, 0.5);
}
.photo-list-item .label[data-v-e1437710] {
    font-family: Oswald;
    font-size: 0.875rem;
    margin-top: auto;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding: 5rem 0 0.75rem;
    background: radial-gradient(
        circle at 50% calc(100% - 0.75rem),
        rgba(0, 0, 0, 0.25) 0,
        transparent 50%
    );
    cursor: pointer;
}
.photo-list-item .label p[data-v-e1437710] {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
}
.photo-list-item.blocked[data-v-e1437710],
.photo-list-item.pink.blocked[data-v-e1437710] {
    cursor: unset;
    pointer-events: none;
    --imBorderBg: linear-gradient(
        180deg,
        transparent 0%,
        rgba(255, 0, 0, 0.25)
    );
    background: linear-gradient(
            143deg,
            rgba(26, 0, 9, 0) 2.22%,
            rgba(255, 0, 25, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    transition: background 0.1s ease, border 0.2s ease;
    display: none;
}
.photo-list-item.pink.rare[data-v-e1437710],
.photo-list-item.rare[data-v-e1437710] {
    cursor: unset;
    pointer-events: none;
    --imBorderBg: linear-gradient(
        338.28deg,
        rgba(255, 184, 0, 0.5),
        rgba(255, 184, 0, 0) 99.13%
    );
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0) 2.22%,
            rgba(255, 184, 0, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    transition: background 0.1s ease, border 0.2s ease;
}
.photo-list-item.halloween[data-v-e1437710],
.photo-list-item.pink.halloween[data-v-e1437710] {
    --imBorderBg: linear-gradient(
        338.28deg,
        rgba(#f79915, 0.2),
        rgba(#ff7422, 0.2) 99.13%
    );
    background: linear-gradient(
            143deg,
            rgba(255, 116, 34, 0.3) 2.22%,
            rgba(247, 153, 21, 0.25) 50%,
            rgba(247, 153, 21, 0.6)
        ),
        rgba(0, 0, 0, 0.55);
    transition: background 0.1s ease, border 0.2s ease;
}
.photo-menu-list-wrapper[data-v-6c0afa3f] {
    display: flex;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    padding: 2rem 1.5rem;
    position: relative;
    height: 100%;
    min-height: 0;
}
.photo-menu-list-wrapper.small[data-v-6c0afa3f] {
    padding: 0;
    background: transparent;
}
.photo-menu-list-wrapper.small .photo-menu-list[data-v-6c0afa3f] {
    margin-right: 0;
    padding: 1.5rem 0.5rem 1.5rem 0;
}
.photo-menu-camera-list[data-v-6c0afa3f] {
    position: absolute;
    left: 0;
    top: 2rem;
    translate: -100% 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.photo-menu-camera-list .clothing-camera[data-v-6c0afa3f] {
    border-radius: 0.5rem 0 0 0.5rem;
    background: var(--dark-green-bg);
    transition: background-color 0.2s ease;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}
.photo-menu-camera-list .clothing-camera[data-v-6c0afa3f]:hover {
    opacity: 0.9;
}
.photo-menu-list[data-v-6c0afa3f] {
    display: grid;
    grid-gap: 0.75rem;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    grid-template-rows: min-content;
    flex: 1;
    overflow: auto;
    margin-right: -2.5rem;
    margin-top: -1rem;
    padding: 1rem 2.5rem 1rem 1px;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.photo-menu-list.disabled[data-v-6c0afa3f] {
    color: hsla(0, 0%, 100%, 0.5) !important;
    pointer-events: none;
}
.photo-menu-list.disabled[data-v-6c0afa3f] img {
    opacity: 0.5;
}
.photo-menu-list[data-v-6c0afa3f]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.photo-menu-list.noGrid[data-v-6c0afa3f] {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Oswald;
    font-size: 2rem;
}
.component-selector-row[data-v-6c9fd2e1] {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    grid-gap: 0.75rem;
    position: relative;
}
.component-selector-row .test-checkbox[data-v-6c9fd2e1] {
    position: absolute;
    top: 0;
    left: 0;
    translate: -100%;
}
.component-selector-row .component-selector-part[data-v-6c9fd2e1] {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 100%;
}
.component-selector-row
    .component-selector-part
    .number-wheel[data-v-6c9fd2e1] {
    margin-left: auto;
}
.component-selector-row.disabled .name[data-v-6c9fd2e1] {
    color: hsla(0, 0%, 100%, 0.5);
}
.component-selector-row.disabled .avail[data-v-6c9fd2e1] {
    color: hsla(0, 0%, 100%, 0.25);
}
.component-selector-row .name[data-v-6c9fd2e1] {
    font-size: 1.5rem;
    text-transform: uppercase;
}
.component-selector-row .avail[data-v-6c9fd2e1] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-size: 1.125rem;
}
.clothing-menu-component-screen[data-v-1a8863b5] {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    min-height: 0;
}
.face-selector-wrapper[data-v-e079ef3e] {
    display: flex;
    flex-direction: column;
}
.face-selector-part[data-v-e079ef3e] {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 100%;
}
.face-selector-part svg[data-v-e079ef3e] {
    width: 1.5rem;
    transition: rotate 0.2s ease;
}
.face-selector-part.open svg[data-v-e079ef3e] {
    rotate: 180deg;
}
.face-selector-part .wheels[data-v-e079ef3e] {
    display: grid;
    grid-template-columns: 12rem 12rem;
    gap: 2rem;
}
.face-selector-part .wheels .wheel[data-v-e079ef3e] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    text-transform: uppercase;
}
.name[data-v-e079ef3e] {
    font-size: 1.5rem;
    text-transform: uppercase;
}
.avail[data-v-e079ef3e] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-size: 1.125rem;
    margin-right: auto;
}
.face-selector-photos[data-v-e079ef3e] {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease, padding 0.4s ease;
}
.face-selector-photos.open[data-v-e079ef3e] {
    padding: 1rem 0;
    max-height: 26rem;
}
.face-selector-photos.open[data-v-e079ef3e] .photo-menu-list,
.face-selector-photos.open[data-v-e079ef3e] .photo-menu-list-wrapper {
    opacity: 1;
    height: 100%;
}
.face-selector-photos.open[data-v-e079ef3e] .photo-menu-list {
    height: 26rem;
}
.face-mix-part[data-v-0128c7cd] {
    padding: 2rem 1.5rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 100%;
    padding-right: 3.5rem;
}
.face-mix-part .name[data-v-0128c7cd] {
    font-size: 1.5rem;
    text-transform: uppercase;
    margin-right: auto;
}
.face-mix-part .sliders[data-v-0128c7cd] {
    display: grid;
    grid-template-columns: 12rem 12rem;
    gap: 2rem;
}
.face-mix-part .sliders .slider[data-v-0128c7cd] {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    text-transform: uppercase;
}
.face-mix-part .sliders .slider p[data-v-0128c7cd] {
    width: 4rem;
}
.face-mix-part .sliders .slider .prp-slider-wrapper[data-v-0128c7cd] {
    width: 100%;
    position: relative;
    bottom: 0.2rem;
}
.face-eye-part[data-v-c280e258] {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 100%;
    padding-right: 3.5rem;
}
.face-eye-part .name[data-v-c280e258] {
    font-size: 1.5rem;
    text-transform: uppercase;
}
.face-eye-part .label[data-v-c280e258] {
    text-transform: uppercase;
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-size: 1.125rem;
    margin-right: auto;
}
.photo-menu-list-wrapper[data-v-c280e258] {
    min-height: 20rem;
}
.face-shape[data-v-e2e46f8a] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.face-shape[data-v-e2e46f8a]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.face-feature-part[data-v-15091dac] {
    padding: 1.875rem 1.5rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 100%;
}
.face-feature-part .name[data-v-15091dac] {
    font-size: 1.5rem;
    text-transform: uppercase;
    width: 10rem;
}
.face-feature-part .prp-slider-wrapper[data-v-15091dac] {
    position: relative;
    bottom: 0.2rem;
    width: 20%;
}
.face-feature-part .number-wheel[data-v-15091dac] {
    margin: -0.5rem auto -0.5rem 0;
}
.grid[data-v-64076ae2] {
    display: grid;
    grid-gap: 0.25rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease, margin 0.5s ease;
    margin-top: 0;
}
.grid.open[data-v-64076ae2] {
    max-height: var(--height);
    margin-top: 0.25rem;
}
.face-feature-category[data-v-64076ae2] {
    padding: 1.75rem 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    line-height: 100%;
    font-size: 1.5rem;
    text-transform: uppercase;
}
.face-feature-category svg[data-v-64076ae2] {
    width: 1.5rem;
    transition: rotate 0.2s ease;
    font-size: 1rem;
}
.face-feature-category.open svg[data-v-64076ae2] {
    rotate: 180deg;
}
.face-features[data-v-260e0990] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.face-features[data-v-260e0990]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.face-features h2[data-v-260e0990] {
    font-size: 2rem;
    text-transform: uppercase;
    margin-right: auto;
    font-weight: 500;
}
.overlay-colors-wrapper[data-v-1d828f3d] {
    padding-top: 0.5rem;
    transition: max-height 0.4s ease, padding 0.5s ease;
    max-height: 0;
    overflow: hidden;
}
.overlay-colors-wrapper.cols[data-v-1d828f3d] {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    grid-gap: 1rem;
}
.overlay-colors-wrapper.cols .grid[data-v-1d828f3d] {
    grid-template-columns: repeat(8, minmax(0, 1fr));
}
.overlay-colors-wrapper.cols.open[data-v-1d828f3d] {
    max-height: 27rem;
}
.overlay-colors-wrapper.open[data-v-1d828f3d] {
    max-height: 17rem;
}
.overlay-colors[data-v-1d828f3d] {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.overlay-colors p[data-v-1d828f3d] {
    font-size: 1.25rem;
}
.grid[data-v-1d828f3d] {
    display: grid;
    grid-gap: 0.5rem;
    grid-template-columns: repeat(16, minmax(0, 1fr));
}
.grid .overlay-color[data-v-1d828f3d] {
    aspect-ratio: 1;
    border-radius: 0.125rem;
    outline: 1px solid hsla(0, 0%, 100%, 0.25);
    cursor: pointer;
    background: rgba(var(--color));
}
.grid .overlay-color.active[data-v-1d828f3d] {
    outline: 3px solid #fff;
}
.overlay-wrapper[data-v-4ef622fd] {
    display: flex;
    flex-direction: column;
}
.overlay-part[data-v-4ef622fd] {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 100%;
}
.overlay-part[data-v-4ef622fd]:not(.hasColor) {
    padding-right: 3.5rem;
}
.overlay-part svg[data-v-4ef622fd] {
    width: 1.5rem;
    transition: rotate 0.2s ease;
}
.overlay-part.open svg[data-v-4ef622fd] {
    rotate: 180deg;
}
.overlay-part.disabled .name[data-v-4ef622fd] {
    color: hsla(0, 0%, 100%, 0.5);
}
.overlay-part.disabled .avail[data-v-4ef622fd] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.1));
}
.overlay-part.disabled .number-wheel[data-v-4ef622fd] {
    pointer-events: none;
    opacity: 0.4;
}
.overlay-part .number-wheel[data-v-4ef622fd] {
    margin-left: 2rem;
}
.overlay-part .prp-slider-wrapper[data-v-4ef622fd] {
    position: relative;
    bottom: 0.2rem;
}
.overlay-part .name[data-v-4ef622fd] {
    font-size: 1.5rem;
    text-transform: uppercase;
}
.overlay-part .avail[data-v-4ef622fd] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-size: 1.125rem;
    margin-right: auto;
}
.face-overlays[data-v-e1c5e530] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.face-overlays[data-v-e1c5e530]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.face-overlays[data-v-7dd5b754] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.face-overlays[data-v-7dd5b754]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.head-hair[data-v-25867394] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.head-hair[data-v-25867394]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.head-hair[data-v-25867394] .photo-menu-list-wrapper {
    max-height: 22rem;
    flex-shrink: 0;
}
.beard[data-v-74f99080] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.beard[data-v-74f99080]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.beard[data-v-74f99080] .photo-menu-list-wrapper {
    max-height: 22rem;
    flex-shrink: 0;
}
.eyebrows[data-v-0b55c7d6] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.eyebrows[data-v-0b55c7d6]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.eyebrows[data-v-0b55c7d6] .photo-menu-list-wrapper {
    max-height: 22rem;
    flex-shrink: 0;
}
.chesthair[data-v-0bf90f02] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.chesthair[data-v-0bf90f02]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.chesthair[data-v-0bf90f02] .photo-menu-list-wrapper {
    max-height: 22rem;
    flex-shrink: 0;
}
.ped-selector-screen[data-v-4b807a2e] {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    min-height: 0;
}
.ped-selector-part[data-v-4b807a2e] {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 100%;
}
.ped-selector-part[data-v-4b807a2e]:not(.hasColor) {
    padding-right: 3.5rem;
}
.ped-selector-part .name[data-v-4b807a2e] {
    font-size: 1.5rem;
    text-transform: uppercase;
}
.ped-selector-part .avail[data-v-4b807a2e] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-size: 1.125rem;
    margin-right: auto;
}
.ped-overlays[data-v-f1e46084] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.ped-overlays[data-v-f1e46084]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.tattoo-list-item[data-v-696a430a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    height: 16rem;
    isolation: isolate;
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(
        180deg,
        transparent 0%,
        hsla(0, 0%, 100%, 0.25)
    );
    border-radius: 0.5rem;
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0) 2.22%,
            rgba(176, 255, 40, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    transition: background 0.1s ease, border 0.2s ease;
}
.tattoo-list-item.active[data-v-696a430a],
.tattoo-list-item[data-v-696a430a]:hover {
    --imBorderBg: #87cd27;
    background: linear-gradient(
            0deg,
            rgba(158, 255, 0, 0.2),
            rgba(158, 255, 0, 0.2)
        ),
        rgba(36, 48, 15, 0.75);
}
.tattoo-list-item .image[data-v-696a430a] {
    position: absolute;
    height: calc(100% - 4rem);
    width: 80%;
    top: 0;
    left: 50%;
    translate: -50% 0;
    z-index: -1;
}
.tattoo-list-item .image img[data-v-696a430a],
.tattoo-list-item .image video[data-v-696a430a] {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
.tattoo-list-item .image svg[data-v-696a430a] {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    font-size: 4rem;
    color: hsla(0, 0%, 100%, 0.5);
}
.tattoo-list-item .label[data-v-696a430a] {
    font-family: Oswald;
    font-size: 0.875rem;
    margin-top: auto;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: 1rem;
    align-items: center;
    padding: 5rem 0 0.75rem;
    background: radial-gradient(
        circle at 50% calc(100% - 0.75rem),
        rgba(0, 0, 0, 0.25) 0,
        transparent 50%
    );
    cursor: pointer;
}
.tattoo-list-item .label p[data-v-696a430a] {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
}
.tattoo-list-item.blocked[data-v-696a430a] {
    cursor: unset;
    pointer-events: none;
    --imBorderBg: linear-gradient(
        180deg,
        transparent 0%,
        rgba(255, 0, 0, 0.25)
    );
    background: linear-gradient(
            143deg,
            rgba(26, 0, 9, 0) 2.22%,
            rgba(255, 0, 25, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    transition: background 0.1s ease, border 0.2s ease;
}
.dropdown-wrapper[data-v-1ec42294] {
    min-width: 15rem;
}
.dropdown-wrapper[data-v-1ec42294] .dropdown-header {
    background: #291921;
}
.dropdown-wrapper[data-v-1ec42294] .dropdown-content {
    background: #291921;
    margin-bottom: 2rem;
}
.search[data-v-1ec42294] {
    width: 100%;
    height: 100%;
    padding: 0;
    border: none;
    background-color: transparent;
    color: #fff;
    font-family: inherit;
    font-size: 1rem;
}
.search[data-v-1ec42294]:focus {
    outline: none;
}
.title[data-v-1ec42294] {
    height: 1.625rem;
    display: flex;
    align-items: center;
}
.runway-prop[data-v-84361de8] {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: var(--dark-green-bg);
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    line-height: 100%;
}
.runway-props[data-v-5011169a] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    flex: 1;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 3rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(180deg, transparent 0, #fff 1rem, #fff);
}
.runway-props[data-v-5011169a]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.add-button[data-v-5011169a] {
    padding: 1.25rem;
    background: var(--dark-green-bg);
    gap: 0.75rem;
    line-height: 100%;
}
.add-button[data-v-5011169a],
[data-v-5011169a] .icon-button {
    border-radius: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}
[data-v-5011169a] .icon-button {
    width: 2.75rem;
    aspect-ratio: 1;
    border: 1px solid transparent;
    background: var(--black);
    transition: background 0.2s ease, border 0.2s ease;
    cursor: pointer;
}
[data-v-5011169a] .icon-button svg {
    transition: all 0.2s ease;
    width: 1.5rem;
}
[data-v-5011169a] .icon-button.active {
    border-radius: 0.5rem;
    border: 1px solid var(--bright-green);
    background: var(--green-bg);
    cursor: unset;
}
[data-v-5011169a] .icon-button.active:hover {
    border: 1px solid var(--bright-green, rgba(158, 255, 0, 0.627));
    background: var(--green-bg);
}
[data-v-5011169a] .icon-button:hover {
    background: var(--black-hover);
    border: 1px solid hsla(0, 0%, 100%, 0.1);
}
[data-v-5011169a] .icon-button:hover img,
[data-v-5011169a] .icon-button:hover svg {
    opacity: 0.8;
}
:root {
    --checkbox-size: 1rem;
}
.prp-toggle {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: calc(var(--checkbox-size) * 2);
    height: var(--checkbox-size);
    background: var(--Black-15, hsla(0, 0%, 7%, 0.15));
    border-radius: calc(var(--checkbox-size) * 2.5);
    border: 0.05rem solid hsla(0, 0%, 100%, 0.15);
    position: relative;
    transition: border-color 0.1s ease-in-out;
    cursor: pointer;
}
.prp-toggle:before {
    content: " ";
    display: block;
    position: absolute;
    width: var(--checkbox-size);
    height: var(--checkbox-size);
    border-radius: 50%;
    background-color: #9e9e9e;
    top: calc(50% - var(--checkbox-size) / 2);
    left: 0;
    transition: left 0.2s ease-in-out, background-color 0.2s ease-in-out;
}
.prp-toggle:checked {
    background: var(--Brand-10, rgba(var(--green-active-rgb), 0.1));
    border-color: rgba(var(--green-active-rgb), 0.5);
}
.prp-toggle:checked:before {
    background-color: var(--green-active);
    left: calc(100% - var(--checkbox-size));
}
.clothing-menu-wrapper[data-v-45e72906] {
    position: absolute;
    color: #fff;
    top: 3.5rem;
    right: 5rem;
    width: 48rem;
    height: calc(100% - 7rem);
    transform: rotateY(-5deg);
    transform-origin: right center;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    --green-bg: #425c2c;
    --dark-green-bg: rgba(26, 31, 20, 0.85);
    --black: rgba(0, 0, 0, 0.5);
    --black-hover: rgba(0, 0, 0, 0.75);
}
.clothing-menu-wrapper.pink[data-v-45e72906] {
    --green-active: #e840a6;
    --bright-green: #fc70c5;
    --green-active-rgb: 232, 64, 166;
    --green-bg: #442a36;
    --dark-green-bg: linear-gradient(
            0deg,
            rgba(255, 140, 196, 0.3),
            rgba(255, 140, 196, 0.3)
        ),
        rgba(0, 0, 0, 0.85);
    --button-bg: rgba(252, 112, 197, 0.8);
    --button-bg-hover: rgba(252, 112, 197, 0.4);
    --black: rgba(0, 0, 0, 0.5);
    --black-hover: rgba(0, 0, 0, 0.75);
}
.buttons[data-v-45e72906] {
    width: 100%;
    margin-top: auto;
    padding-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.buttons .naked-toggle[data-v-45e72906] {
    display: flex;
    padding: 0.75rem 1rem;
    height: -moz-fit-content;
    height: fit-content;
    align-items: center;
    justify-content: center;
    width: -moz-fit-content;
    width: fit-content;
    gap: 0.6125rem;
    color: var(--White, #fff);
    font-family: Oswald;
    font-size: 1rem;
    cursor: pointer;
    line-height: 1;
    transition: background 0.2s ease;
    border-radius: var(--border-radius-rounded-lg, 0.5rem);
    background: var(--dark-green-bg);
}
.wrapper[data-v-2619c0f6] {
    perspective: var(--perspective);
}
.cd-flare[data-v-b6d672ae] {
    border-radius: 0.25rem;
    padding: var(--gap-gap-15, 0.375rem);
    border: 1px solid transparent;
    background: var(--White-White-10, hsla(0, 0%, 100%, 0.1));
    --imBorderBg: linear-gradient(180deg, hsla(0, 0%, 100%, 0.1) 0%, gray);
    --imBorderWidth: 1px;
    color: #fff;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 500;
    flex-shrink: 0;
    width: -moz-fit-content;
    width: fit-content;
    line-height: 100%;
}
.cd-flare.icon[data-v-b6d672ae] {
    padding: var(--gap-gap-15, 0.25rem);
}
.cd-flare.active[data-v-b6d672ae] {
    --imBorderBg: linear-gradient(180deg, hsla(0, 0%, 100%, 0.1) 0%, #87da21);
}
.cd-flare.success[data-v-b6d672ae] {
    background: rgba(176, 255, 40, 0.15);
    --imBorderBg: linear-gradient(180deg, transparent 0%, #87da21 80%);
}
.cd-flare.error[data-v-b6d672ae] {
    background: rgba(255, 0, 0, 0.15);
    --imBorderBg: linear-gradient(180deg, transparent 0%, red 80%);
}
.cd-flare.unicorn[data-v-b6d672ae] {
    background: rgba(255, 73, 105, 0.15);
    --imBorderBg: linear-gradient(180deg, transparent 0%, #ff4969 80%);
}
.cd-flare.hot[data-v-b6d672ae] {
    border: 1px solid #ff0e0e;
    background: linear-gradient(
            180deg,
            rgba(255, 14, 14, 0.1) 2.7%,
            rgba(207, 186, 0, 0.1) 97.61%
        ),
        linear-gradient(
            180deg,
            rgba(255, 14, 14, 0.1) 2.7%,
            rgba(207, 186, 0, 0.1) 97.61%
        );
    box-shadow: 0 4px 24px 0 rgba(207, 186, 0, 0.25);
    --imBorderBg: linear-gradient(0deg, #ff0e0e 2.11%, #cfba00);
    --imBorderWidth: 1px;
}
.cd-flare[data-v-b6d672ae] svg {
    width: 100%;
    height: 100%;
}
.vehicle-thumbnail[data-v-39f724d8] {
    width: 17.125rem;
    height: 100%;
    border-radius: 0.75rem;
    transition: background 0.2s ease, border 0.2s ease;
    isolation: isolate;
    cursor: pointer;
    padding: 1rem;
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(
        to bottom right,
        hsla(0, 0%, 100%, 0.05),
        hsla(0, 0%, 100%, 0.2) 50%,
        #87cd27
    );
    border: 1px solid transparent;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.vehicle-thumbnail[data-v-39f724d8]:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: inherit;
    width: 100%;
    height: 100%;
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0) 2.22%,
            rgba(176, 255, 40, 0.25)
        ),
        rgba(0, 0, 0, 0.55);
    transition: opacity 0.2s ease;
}
.vehicle-thumbnail.outOfStock[data-v-39f724d8] {
    border: 1px solid hsla(0, 0%, 100%, 0.25);
}
.vehicle-thumbnail.outOfStock[data-v-39f724d8]:before {
    opacity: 0;
}
.vehicle-thumbnail.outOfStock[data-v-39f724d8]:after {
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0) 2.22%,
            hsla(0, 0%, 100%, 0.25)
        ),
        linear-gradient(
            107deg,
            rgba(0, 0, 0, 0.55),
            hsla(0, 0%, 8%, 0.38) 54.5%,
            rgba(0, 0, 0, 0.55)
        );
}
.vehicle-thumbnail.outOfStock.active[data-v-39f724d8],
.vehicle-thumbnail.outOfStock[data-v-39f724d8]:hover {
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: hsla(0, 0%, 100%, 0.2);
}
.vehicle-thumbnail.active[data-v-39f724d8]:not(.outOfStock) {
    border: 1px solid #87da21;
}
.vehicle-thumbnail.active[data-v-39f724d8]:not(.outOfStock):after {
    background: linear-gradient(
            143deg,
            rgba(20, 26, 9, 0.25) -5.22%,
            rgba(176, 255, 40, 0.4)
        ),
        rgba(0, 0, 0, 0.25);
}
.vehicle-thumbnail[data-v-39f724d8]:hover {
    border: 1px solid #87da21;
    background: rgba(58, 86, 24, 0.8);
}
.vehicle-thumbnail[data-v-39f724d8]:hover:after,
.vehicle-thumbnail[data-v-39f724d8]:hover:before {
    opacity: 0;
}
.vehicle-thumbnail .background[data-v-39f724d8] {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.vehicle-thumbnail .background img[data-v-39f724d8] {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
.vehicle-thumbnail .row[data-v-39f724d8] {
    display: flex;
    align-items: center;
}
.vehicle-thumbnail .row .label[data-v-39f724d8] {
    margin-right: auto;
}
.vehicle-thumbnail .row .flares[data-v-39f724d8] {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}
.vehicle-thumbnail .popper[data-v-39f724d8] {
    font-family: Geist;
    font-size: 0.875rem;
}
.vehicle-carousel-wrapper[data-v-271c13a8] {
    width: 100%;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}
.vehicle-carousel-wrapper .row[data-v-271c13a8] {
    display: flex;
    align-items: center;
    gap: 2.1875rem;
}
.vehicle-carousel-wrapper .row .wide[data-v-271c13a8] {
    width: 15.2rem;
    font-family: Geist;
}
.vehicle-carousel-wrapper .row .wide .prp-input[data-v-271c13a8] {
    background: #0b1203;
}
.vehicle-carousel-wrapper .row .wide[data-v-271c13a8] .dropdown-header {
    font-weight: 400 !important;
}
.vehicle-carousel-wrapper .icon[data-v-271c13a8] {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: rgba(11, 18, 3, 0.65);
    transition: background 0.2s ease;
}
.vehicle-carousel-wrapper .icon[data-v-271c13a8]:hover {
    background: rgba(11, 18, 3, 0.25);
}
.vehicle-carousel-wrapper .carousel[data-v-271c13a8] {
    width: 100%;
    margin: auto;
    height: 11rem;
    display: grid;
    align-items: center;
    grid-template-columns: 2.5rem 1fr 2.5rem;
    min-width: 0;
    isolation: isolate;
}
.vehicle-carousel-wrapper .carousel .scroll[data-v-271c13a8] {
    min-width: 0;
    height: 100%;
    overflow-x: auto;
    display: flex;
    scroll-margin: 0;
    padding: 0.5rem 1rem;
    margin: 0 -1rem;
    z-index: -1;
    -webkit-mask: linear-gradient(
        90deg,
        transparent 0,
        #fff 1.625rem,
        #fff calc(100% - 1.625rem),
        transparent
    );
}
.vehicle-carousel-wrapper
    .carousel
    .scroll[data-v-271c13a8]::-webkit-scrollbar {
    display: none;
}
.vehicle-carousel-wrapper .carousel .scroll .page[data-v-271c13a8] {
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(var(--itemCount, 4), 1fr);
    justify-items: center;
    align-items: center;
}
[data-v-271c13a8] .prp-button {
    border-radius: 0.375rem;
    border: 1.5px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: rgba(11, 18, 3, 0.8);
}
.vehicle-stats[data-v-5c8b88b6] {
    width: 25rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: 1rem;
}
.vehicle-stats .green[data-v-5c8b88b6]:not(.prp-button) {
    color: #87da21;
}
.vehicle-stats .uppercase[data-v-5c8b88b6] {
    text-transform: uppercase;
}
.vehicle-stats .gray[data-v-5c8b88b6] {
    color: hsla(0, 0%, 100%, 0.5);
}
.vehicle-stats .row[data-v-5c8b88b6] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1.5rem;
    gap: 2rem;
}
.vehicle-stats .row .part[data-v-5c8b88b6] {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.vehicle-stats .row .part svg[data-v-5c8b88b6] {
    font-size: 1rem;
}
.vehicle-stats .items-start[data-v-5c8b88b6] {
    align-items: start !important;
}
.vehicle-stats .col[data-v-5c8b88b6] {
    display: flex;
    flex-direction: column;
}
.vehicle-stats .col[data-v-5c8b88b6]:last-child {
    align-items: flex-end;
}
.vehicle-stats .stats[data-v-5c8b88b6] {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.vehicle-stats .progress-outer[data-v-5c8b88b6] {
    height: 0.9375rem;
    width: 100%;
    border-radius: 0.25rem;
    background: hsla(0, 0%, 100%, 0.1);
    overflow: hidden;
}
.vehicle-stats .progress-outer .progress-inner[data-v-5c8b88b6] {
    height: 100%;
    width: 100%;
    border-radius: 0.25rem;
    background: #87da21;
    transition: scale 0.6s ease;
    scale: var(--prog) 1;
    transform-origin: left center;
}
.vehicle-stats .buttons[data-v-5c8b88b6] {
    display: flex;
    gap: 2rem;
}
.vehicle-stats .buttons .prp-button[data-v-5c8b88b6] {
    flex: 1;
    font-size: 1.25rem;
    padding: var(--gap-gap-4, 1rem) var(--gap-gap-6, 1.5rem);
}
.vehicle-colors[data-v-0422dc32] {
    min-width: 15rem;
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.vehicle-colors .row[data-v-0422dc32] {
    display: flex;
    justify-content: space-between;
}
.vehicle-colors[data-v-0422dc32] .dropdown-wrapper {
    z-index: 3 !important;
}
.vehicle-colors .colors[data-v-0422dc32] {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-gap: 0.5rem;
}
.vehicle-colors .color[data-v-0422dc32] {
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 0.125rem;
    background: var(--col);
    cursor: pointer;
}
[data-v-0422dc32] .prp-button {
    border-radius: 0.375rem;
    border: 1.5px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: rgba(11, 18, 3, 0.8);
    width: 100%;
}
.car-dealer-menu[data-v-66b7677a] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        #000 10%,
        transparent 50%,
        transparent 0,
        #000 90%
    );
    display: flex;
    flex-direction: column;
    gap: 4.5rem;
    color: #fff;
    padding: 3.5rem 9.7125rem;
    min-width: 0;
}
.car-dealer-menu .row[data-v-66b7677a] {
    width: 100%;
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.countdown[data-v-4bae585c] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10rem;
}
.countdown p[data-v-4bae585c] {
    opacity: 0;
    font-weight: 700;
    color: #fff;
}
.countdown p.animation[data-v-4bae585c] {
    animation: countdown-4bae585c 0.85s ease;
}
@keyframes countdown-4bae585c {
    0% {
        font-size: 0.5em;
        opacity: 0;
    }
    30% {
        opacity: 0.7;
    }
    to {
        font-size: 1em;
        opacity: 0;
    }
}
.map-wrapper[data-v-0a79b454] {
    position: absolute;
    width: calc(100% - 35.5125rem);
    height: calc(100% - 10rem);
    left: 5.5125rem;
    top: 50%;
    translate: 0 -50%;
    display: flex;
    flex-direction: column;
    gap: 1.75rem;
}
.map-wrapper .label[data-v-0a79b454] {
    color: #fff;
    font-size: 2.25rem;
    font-weight: 400;
}
.map-wrapper .map[data-v-0a79b454] {
    flex: 1;
    width: 100%;
}
.wrapper[data-v-6f21167e] {
    perspective: var(--perspective);
    overflow: visible;
}
.wrapper.bg[data-v-6f21167e] {
    background: linear-gradient(122deg, #000f1e, rgba(0, 15, 30, 0) 69.38%),
        linear-gradient(122deg, rgba(34, 0, 0, 0), rgba(34, 0, 0, 0.85) 69.38%);
}
.calls[data-v-6f21167e] {
    display: flex;
    flex-direction: column;
    transform: rotateY(-8deg);
    transform-origin: right top;
    gap: 1.5rem;
    width: 20rem;
    min-height: 5rem;
    position: absolute;
    top: 3.5rem;
    right: 1.75rem;
    overflow: auto;
    max-height: calc(100% - 5rem);
    margin-top: -1rem;
    padding: 1rem 0 1rem 1px;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.calls[data-v-6f21167e]::-webkit-scrollbar {
    display: none;
}
.notification-enter-active[data-v-6f21167e],
.notification-leave-active[data-v-6f21167e],
.notification-move[data-v-6f21167e] {
    transition: opacity 0.5s ease, transform 0.5s ease;
}
.notification-enter-from[data-v-6f21167e],
.notification-leave-to[data-v-6f21167e] {
    opacity: 0;
}
.notification-enter-from[data-v-6f21167e] {
    transform: translateX(100%);
}
.welcome-wrapper[data-v-28bee357] {
    width: 98rem;
    height: 51.875rem;
    display: flex;
    position: relative;
    background: linear-gradient(
            100deg,
            rgba(135, 218, 33, 0.05) 0.38%,
            rgba(79, 124, 22, 0.05) 98.25%
        ),
        #0b0b0b;
}
.welcome-wrapper .media[data-v-28bee357] {
    width: 53.5rem;
    height: 51.875rem;
}
.welcome-wrapper .media img[data-v-28bee357],
.welcome-wrapper .media video[data-v-28bee357] {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.welcome-wrapper .action-space[data-v-28bee357] {
    position: absolute;
    left: 50%;
    bottom: 0;
    translate: -50% calc(100% + 1.5rem);
    display: flex;
    gap: 2rem;
}
.welcome-wrapper .content[data-v-28bee357] {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4.15625rem;
    padding: var(--gap-gap-0, 3.0625rem) var(--gap-gap-0, 2.25rem);
    color: #fff;
}
.welcome-wrapper .content h1[data-v-28bee357] {
    font-size: 2rem;
    font-weight: 400;
}
.welcome-wrapper .content .row[data-v-28bee357] {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.welcome-wrapper .content .parts[data-v-28bee357] {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    overflow: auto;
}
.welcome-wrapper .content .parts .spacer[data-v-28bee357] {
    height: 1px;
    width: 100%;
    background: hsla(0, 0%, 100%, 0.1);
}
.welcome-wrapper .content .parts .part[data-v-28bee357] {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}
.welcome-wrapper .content .parts .part h2[data-v-28bee357] {
    font-size: 2rem;
    font-weight: 400;
}
.welcome-wrapper .content .parts .part p[data-v-28bee357] {
    font-family: Geist;
    font-size: 1rem;
}
.wrapper[data-v-3bb5c2a9] {
    perspective: var(--perspective);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background 0.5s ease;
}
.wrapper.bg[data-v-3bb5c2a9] {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5));
}
.ped-dialog-option[data-v-07d4014f] {
    width: 100%;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.45);
    background: rgba(0, 0, 0, 0.5);
    transition: background 0.2s ease, border 0.2s ease;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    cursor: pointer;
}
.ped-dialog-option.center[data-v-07d4014f] {
    position: relative;
    padding: 1rem 4rem;
    justify-content: center;
    text-align: center;
}
.ped-dialog-option.center svg[data-v-07d4014f] {
    position: absolute;
    left: 2rem;
}
.ped-dialog-option[data-v-07d4014f]:hover {
    border: 1px solid hsla(0, 0%, 100%, 0.65);
    background: rgba(0, 0, 0, 0.3);
}
.ped-dialog-option .col[data-v-07d4014f] {
    display: flex;
    flex-direction: column;
    gap: 0.3125rem;
}
.ped-dialog-option h1[data-v-07d4014f] {
    font-family: Oswald;
    font-weight: 400;
    font-size: 1.125rem;
    text-transform: uppercase;
    line-height: 1.688rem;
}
.ped-dialog-option p[data-v-07d4014f] {
    color: hsla(0, 0%, 93%, 0.65);
    font-family: Geist;
    font-size: 0.875rem;
    line-height: 150%;
}
.ped-dialog-view[data-v-ef720bbc] {
    perspective: var(--perspective);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #fff;
}
.ped-dialog-view .menu[data-v-ef720bbc] {
    width: 20rem;
    position: absolute;
    top: 50%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.ped-dialog-view .menu.two[data-v-ef720bbc] {
    width: 45.75rem;
    position: unset;
    display: grid;
    grid-template-columns: 1fr 1fr;
}
.ped-dialog-view .menu.left[data-v-ef720bbc] {
    translate: -50% -50%;
    left: calc(50% - 28.5rem);
}
.ped-dialog-view .menu.right[data-v-ef720bbc] {
    translate: 50% -50%;
    right: calc(50% - 28.5rem);
}
.ped-dialog-view .bottom-bar[data-v-ef720bbc] {
    width: 35rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.8125rem;
    position: absolute;
    bottom: 5rem;
    left: 50%;
    translate: -50% 0;
}
.ped-dialog-view .bottom-bar h2[data-v-ef720bbc] {
    text-align: center;
    font-family: Oswald;
    font-size: 2rem;
    font-weight: 400;
    text-transform: uppercase;
    margin: -1rem 0;
}
.ped-dialog-view .bottom-bar > p[data-v-ef720bbc] {
    text-align: center;
    color: #fff;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.25);
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 150%;
}
.ped-dialog-view .bottom-bar .title[data-v-ef720bbc] {
    color: var(--Brand-Brand, #87da21);
    font-family: Oswald;
    font-size: 1rem;
    font-weight: 400;
    text-transform: uppercase;
}
.ped-dialog-view .bottom-bar .progress[data-v-ef720bbc] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    font-family: Oswald;
    font-size: 1rem;
    width: 25rem;
}
.wrapper[data-v-7f49291f] {
    perspective: var(--perspective);
    transition: background 0.5s ease;
}
.wrapper.bg[data-v-7f49291f] {
    background: linear-gradient(
            90deg,
            rgba(0, 0, 0, 0.8) 11.7%,
            transparent 48.26%
        ),
        linear-gradient(90deg, transparent 48.61%, rgba(0, 0, 0, 0.8) 81.75%);
}
.wrapper[data-v-04a25f17] {
    perspective: var(--perspective);
}
.wrapper .hud[data-v-04a25f17] {
    width: 100%;
    padding: 5rem 1.5rem;
    top: 50%;
    translate: 0 -50%;
    overflow: visible;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: flex-end;
    color: #fff;
    position: relative;
    isolation: isolate;
    transform: rotateY(-8deg);
    transform-origin: right center;
}
.wrapper .hud[data-v-04a25f17]:before {
    position: absolute;
    top: 0;
    right: 1.5rem;
    content: "";
    width: 5.125rem;
    height: 100%;
    border-radius: 1.5rem;
    background: #1b1c2c;
    filter: blur(3rem);
    z-index: -1;
}
.wrapper .part[data-v-04a25f17] {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.task[data-v-3e761d60] {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    padding: 3rem 2rem;
    border-radius: 0.5rem;
    border: 1px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: #121212;
    transition: background 0.2s ease;
    gap: 1rem;
    max-width: 20rem;
    position: relative;
}
.task.canClick[data-v-3e761d60] {
    cursor: pointer;
}
.task[data-v-3e761d60]:hover {
    border-radius: 0.5rem;
    border: 1px solid var(--White-White-15, hsla(0, 0%, 100%, 0.2));
    background: #1c1c1c;
}
.task.active[data-v-3e761d60] {
    border-radius: 0.5rem;
    border: 1px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: linear-gradient(161deg, transparent, rgba(135, 218, 33, 0.2)),
        #121212;
    cursor: unset;
}
.task h3[data-v-3e761d60] {
    font-family: Oswald;
    font-size: 2rem;
    font-weight: 400;
    text-align: center;
    text-transform: uppercase;
    margin: auto 0;
}
.task .active-label[data-v-3e761d60] {
    border-radius: 1rem;
    border: 1px solid var(--Bright-Green, #9eff00);
    background: rgba(158, 255, 0, 0.1);
    top: 0;
    translate: -50% -50%;
}
.task .active-label[data-v-3e761d60],
.task .reset-button[data-v-3e761d60] {
    padding: var(--gap-gap-3, 0.75rem);
    width: -moz-fit-content;
    width: fit-content;
    line-height: 100%;
    position: absolute;
    left: 50%;
}
.task .reset-button[data-v-3e761d60] {
    border-radius: 1rem;
    border: 1px solid red;
    background: rgba(255, 0, 0, 0.1);
    bottom: 0;
    translate: -50% 50%;
    cursor: pointer;
    transition: background 0.2s ease;
}
.task .reset-button[data-v-3e761d60]:hover {
    background: rgba(255, 0, 0, 0.2);
}
.task .parts[data-v-3e761d60] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.task .parts .part[data-v-3e761d60] {
    width: 12rem;
    display: flex;
    align-items: center;
    gap: 1.0625rem;
}
.task .parts .part svg[data-v-3e761d60] {
    font-size: 1.325rem;
    color: #87da21;
}
.task .parts .part .col[data-v-3e761d60] {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    font-family: Oswald;
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 100%;
}
.task .parts .part .col .dark[data-v-3e761d60] {
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 500;
    color: hsla(0, 0%, 100%, 0.5);
}
.task .job-label[data-v-3e761d60] {
    font-size: 1.25rem;
    text-transform: uppercase;
    font-weight: 400;
    color: hsla(0, 0%, 100%, 0.5);
}
.top-bar-progress-wrapper[data-v-156d4aa6] {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.top-bar-progress-wrapper .images[data-v-156d4aa6] {
    width: 100%;
    height: 4rem;
    position: relative;
}
.top-bar-progress-wrapper .images .image[data-v-156d4aa6] {
    position: absolute;
    height: inherit;
    top: 50%;
    left: calc(var(--progress) * 100%);
    translate: -50% -50%;
}
.top-bar-progress-wrapper .images .image.bw[data-v-156d4aa6] {
    filter: grayscale(100%);
}
.top-bar-progress-wrapper .texts[data-v-156d4aa6] {
    width: 100%;
    height: 1.5rem;
    position: relative;
}
.top-bar-progress-wrapper .texts .text[data-v-156d4aa6] {
    position: absolute;
    top: 50%;
    left: calc(var(--progress) * 100%);
    translate: -50% -50%;
    font-size: 1.5rem;
}
.top-bar-progress-wrapper .progress-wrapper-wrapper[data-v-156d4aa6] {
    width: 100%;
    height: 1.25rem;
    border-radius: 1.25rem;
    background: rgba(0, 0, 0, 0.5);
    position: relative;
}
.top-bar-progress-wrapper
    .progress-wrapper-wrapper
    .progress-wrapper[data-v-156d4aa6] {
    width: 100%;
    height: 100%;
    border-radius: inherit;
    overflow: hidden;
}
.top-bar-progress-wrapper
    .progress-wrapper-wrapper
    .progress-wrapper
    .progress[data-v-156d4aa6] {
    height: 100%;
    width: 100%;
    background: #87da21;
    box-shadow: 0 4px 4px 0 rgba(135, 218, 33, 0.25);
    transform-origin: left center;
    scale: var(--progress) 1;
}
.top-bar-progress-wrapper .progress-wrapper-wrapper .tick[data-v-156d4aa6] {
    position: absolute;
    height: 1.875rem;
    width: 0.375rem;
    border-radius: 1.5rem;
    border: 1px solid #87da21;
    background: rgba(135, 218, 33, 0.75);
    box-shadow: 0 4px 4px 0 rgba(135, 218, 33, 0.5);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    top: 50%;
    left: calc(var(--progress) * 100%);
    translate: -50% -50%;
}
.top-bar-progress-wrapper .progress-wrapper-wrapper .tick.bw[data-v-156d4aa6] {
    border: none;
    background: var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    filter: grayscale(100%);
    box-shadow: none;
}
.top-bar[data-v-5a602610] {
    width: 100%;
    padding: var(--gap-gap-6, 1.5rem) var(--gap-gap-10, 2.5rem);
    border-radius: 0.5rem;
    border: 1px solid var(--White-White-15, hsla(0, 0%, 100%, 0.15));
    background: #1e1e1e;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3rem;
}
.top-bar h1[data-v-5a602610] {
    font-family: Oswald;
    font-size: 2rem;
    font-weight: 400;
}
.top-bar .col[data-v-5a602610] {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.top-bar .col .field[data-v-5a602610] {
    display: flex;
    flex-direction: column;
    color: #87da21;
    font-size: 2rem;
    font-weight: 400;
}
.top-bar .col .field .dark[data-v-5a602610] {
    color: hsla(0, 0%, 100%, 0.75);
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 400;
}
.job-board-view[data-v-c6cf08c0] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    min-width: 56rem;
    border-radius: 0.5rem;
    background: hsla(0, 0%, 9%, 0.85);
    color: #fff;
}
.job-board-view .tasks[data-v-c6cf08c0] {
    display: flex;
    justify-content: center;
    gap: 2rem;
}
.wrapper[data-v-d34a0d2c] {
    display: flex;
    justify-content: center;
    align-items: center;
}
.wrapper[data-v-b51fa056] {
    perspective: var(--perspective);
    isolation: isolate;
}
.wrapper[data-v-b51fa056]:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 2s ease;
    background: linear-gradient(
            285deg,
            rgba(255, 0, 0, 0.2),
            rgba(237, 0, 0, 0) 45.13%
        ),
        linear-gradient(285deg, rgba(255, 0, 0, 0.2), rgba(237, 0, 0, 0) 45.13%),
        linear-gradient(
            249deg,
            rgba(237, 0, 0, 0) 71.48%,
            rgba(255, 92, 0, 0.2)
        ),
        linear-gradient(
            236deg,
            rgba(237, 0, 0, 0) 50.84%,
            rgba(255, 92, 0, 0.2) 89.8%
        );
}
.wrapper.bg[data-v-b51fa056]:before {
    opacity: 1;
    animation: anim-opacity-b51fa056 5s infinite;
    animation-delay: 2s;
}
.wrapper .field[data-v-b51fa056] {
    position: absolute;
    font-size: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    text-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.5);
    top: 50%;
    left: 50%;
    translate: -50% -50%;
}
.wrapper .field.left[data-v-b51fa056] {
    left: calc(50% - 25rem);
}
.wrapper .field.right[data-v-b51fa056] {
    left: calc(50% + 25rem);
}
.wrapper .field .box[data-v-b51fa056] {
    padding: var(--gap-gap-3, 0.75rem) 1rem;
    border-radius: var(--border-radius-rounded, 0.25rem);
    border: 1px solid #ff5c00;
    background: linear-gradient(
            180deg,
            rgba(237, 0, 0, 0),
            rgba(255, 92, 0, 0.2)
        ),
        rgba(0, 0, 0, 0.5);
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
    width: -moz-fit-content;
    width: fit-content;
}
@keyframes anim-opacity-b51fa056 {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.75;
    }
    to {
        opacity: 1;
    }
}
.search-input[data-v-1c4d381d] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: rgba(0, 0, 0, 0.55);
    color: #fff;
    width: calc(100% - 6rem);
}
.search-input input[data-v-1c4d381d] {
    outline: none;
    border: none;
    background: transparent;
    color: #fff;
    caret-color: #fff;
    padding: 0;
    margin: 0;
}
.contact-list[data-v-1c4d381d] {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding-left: 3rem;
    padding-right: 4rem;
    margin-right: -1rem;
    padding-bottom: 2rem;
    -webkit-mask: linear-gradient(180deg, #fff calc(100% - 5rem), transparent);
}
.contact-list[data-v-1c4d381d] > * + * {
    border-top-width: 1px;
    border-bottom-width: 0;
    border-left-width: 0;
    border-right-width: 0;
    border-style: solid;
    margin-top: 1.125rem;
    padding-top: 1.125rem;
    border-color: hsla(0, 0%, 100%, 0.1);
}
.contact-list .contact[data-v-1c4d381d] {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.contact-list .contact .labels[data-v-1c4d381d] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    color: #fff;
    font-family: Geist, sans-serif;
}
.contact-list .contact .labels[data-v-1c4d381d]:first-child {
    font-size: 1rem;
    font-weight: 600;
}
.contact-list .contact .labels .number[data-v-1c4d381d] {
    font-size: 0.75rem;
    font-weight: 400;
}
.contact-list .contact .call-button[data-v-1c4d381d] {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1rem;
    border-radius: 50%;
    border: 1px solid #87da21;
    background: rgba(135, 218, 33, 0.65);
    color: #fff;
    cursor: pointer;
    transition: background 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.contact-list .contact .call-button[data-v-1c4d381d]:hover {
    background: rgba(135, 218, 33, 0.35);
}
.wrapper[data-v-bca500da] {
    perspective: var(--perspective);
}
.inner-wrapper[data-v-bca500da] {
    width: 100%;
    height: 100%;
    perspective: var(--perspective);
    transform-origin: right center;
    transform: rotateY(-8deg);
}
.phone-booth-wrapper[data-v-bca500da] {
    position: absolute;
    top: 50%;
    left: 70%;
    font-family: Geist, sans-serif;
    translate: -50% -50%;
    background: rgba(26, 31, 20, 0.85);
    border-radius: 0.5rem;
    z-index: 0;
    box-sizing: border-box;
    width: 22rem;
    height: 42rem;
}
.phone-booth-wrapper .container[data-v-bca500da] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem 0;
    width: 100%;
    height: 100%;
}
.phone-booth-wrapper[data-v-bca500da]:before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 0.5rem;
    padding: 1px;
    background: linear-gradient(
        150deg,
        hsla(0, 0%, 100%, 0.15),
        hsla(0, 0%, 100%, 0.5)
    );
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: -1;
}
.phone-booth-wrapper .phone-booth-number[data-v-bca500da] {
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: rgba(0, 0, 0, 0.55);
    font-size: 1.5rem;
    font-family: Geist, sans-serif;
    color: #fff;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 4rem;
}
.phone-booth-wrapper .phone-booth-keypad-wrapper[data-v-bca500da] {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    row-gap: 1rem;
    -moz-column-gap: 1rem;
    column-gap: 1rem;
    width: 100%;
}
.phone-booth-wrapper
    .phone-booth-keypad-wrapper
    .phone-booth-keypad-key[data-v-bca500da] {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: rgba(0, 0, 0, 0.25);
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 2.5rem;
    color: #fff;
    cursor: pointer;
    transition: border, background 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.phone-booth-wrapper
    .phone-booth-keypad-wrapper
    .phone-booth-keypad-key
    svg[data-v-bca500da] {
    width: 1.5rem;
    height: 1.5rem;
}
.phone-booth-wrapper
    .phone-booth-keypad-wrapper
    .phone-booth-keypad-key[data-v-bca500da]:hover {
    border: 1px solid #87da21;
    background: rgba(135, 218, 33, 0.2);
}
.phone-booth-wrapper .duration[data-v-bca500da] {
    color: #fff;
    font-size: 1rem;
    flex: 1;
}
.phone-booth-wrapper .phone-booth-keypad-action[data-v-bca500da] {
    width: 4rem;
    height: 4rem;
    border-radius: 100%;
    border: 1px solid #87da21;
    background: rgba(135, 218, 33, 0.65);
    margin-top: 0.5rem;
    color: #fff;
    font-size: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.phone-booth-wrapper .phone-booth-keypad-action.red[data-v-bca500da] {
    background: rgba(255, 0, 0, 0.35);
    border: 1px solid red;
}
.phone-booth-wrapper .phone-booth-keypad-action.red[data-v-bca500da]:hover {
    background: rgba(255, 0, 0, 0.15);
}
.phone-booth-wrapper .phone-booth-keypad-action[data-v-bca500da]:hover {
    background: rgba(135, 218, 33, 0.35);
}
.phone-booth-wrapper .phone-booth-header[data-v-bca500da] {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.6rem;
    width: 100%;
}
.phone-booth-wrapper .phone-booth-header span[data-v-bca500da] {
    font-family: Oswald, sans-serif;
    font-size: 1.25rem;
    text-align: center;
    color: hsla(0, 0%, 100%, 0.5);
    text-transform: uppercase;
    cursor: pointer;
    transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.phone-booth-wrapper .phone-booth-header span.active[data-v-bca500da],
.phone-booth-wrapper .phone-booth-header span[data-v-bca500da]:hover {
    color: #87da21;
}
.image[data-v-7709126a] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
}
.image img[data-v-7709126a] {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
.code-wrapper[data-v-13f34432] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25rem;
    height: 15rem;
    isolation: isolate;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Permanent Marker;
    font-size: 6rem;
    letter-spacing: 1.5rem;
    font-weight: 400;
}
.code-wrapper .code[data-v-13f34432] {
    box-sizing: content-box;
    text-align: center;
}
.code-wrapper .image[data-v-13f34432] {
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
}
.code-wrapper .image img[data-v-13f34432] {
    position: absolute;
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
.drug-slider-wrapper[data-v-04b0eb1a] {
    width: 25rem;
    display: flex;
    flex-direction: column;
    gap: 1.125rem;
}
.drug-slider-wrapper .row[data-v-04b0eb1a] {
    display: flex;
    justify-content: space-between;
}
.drug-slider-wrapper .row .max[data-v-04b0eb1a],
.drug-slider-wrapper .row .min[data-v-04b0eb1a] {
    color: #afafaf;
    font-size: 1rem;
    font-weight: 400;
    text-transform: uppercase;
    opacity: 0;
    transition: opacity 0.2s ease;
}
.drug-slider-wrapper .value[data-v-04b0eb1a] {
    color: #87da21;
    font-size: 1rem;
    font-weight: 400;
    text-transform: uppercase;
    position: absolute;
    text-align: center;
    translate: calc(var(--progress) * -1 * 100%) 0;
}
.drug-slider-wrapper .visible[data-v-04b0eb1a] {
    opacity: 1 !important;
    transition-duration: 0.75s;
}
input[type="range"][data-v-04b0eb1a] {
    -webkit-appearance: none;
    width: 100%;
    background: transparent;
}
input[type="range"][data-v-04b0eb1a]::-webkit-slider-thumb {
    -webkit-appearance: none;
}
input[type="range"][data-v-04b0eb1a]:focus {
    outline: none;
}
input[type="range"][data-v-04b0eb1a]::-webkit-slider-thumb {
    width: 0.875rem;
    height: 2rem;
    flex-shrink: 0;
    border-radius: 0.25rem;
    position: relative;
    border: 1px solid hsla(0, 0%, 100%, 0.2);
    background: linear-gradient(169deg, #68dd00 1.04%, #87da21 98.43%);
    box-shadow: 0 4px 24px 0 rgba(104, 221, 0, 0.5);
    top: 50%;
    translate: 0 -50%;
}
input[type="range"][data-v-04b0eb1a]::-webkit-slider-runnable-track {
    width: 100%;
    height: 0.9375rem;
    cursor: pointer;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 93%, 0.1);
    background: rgba(0, 0, 0, 0.5);
}
.drug-sell-view[data-v-1d512e95] {
    position: absolute;
    bottom: 5rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    color: #fff;
    isolation: isolate;
    max-width: 40rem;
}
.drug-sell-view .bg[data-v-1d512e95] {
    position: absolute;
    width: 36.87525rem;
    height: 36.87525rem;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.55);
    filter: blur(8rem);
    top: -5rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: -1;
}
.drug-sell-view .labels[data-v-1d512e95] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    text-align: center;
    text-transform: uppercase;
}
.drug-sell-view .labels h3[data-v-1d512e95] {
    font-size: 2rem;
    font-style: normal;
    font-weight: 400;
}
.wrapper[data-v-3338238c] {
    --green-active: #e840a6;
    --bright-green: #e840a6;
    --green-active-rgb: 232, 64, 166;
    --green-bg: #442a36;
    --dark-green-bg: linear-gradient(
            0deg,
            rgba(255, 140, 196, 0.3),
            rgba(255, 140, 196, 0.3)
        ),
        rgba(0, 0, 0, 0.85);
    --button-bg: rgba(252, 112, 197, 0.8);
    --button-bg-hover: rgba(252, 112, 197, 0.4);
    --black: rgba(0, 0, 0, 0.5);
    --black-hover: rgba(0, 0, 0, 0.75);
}
.runway-theme-modal[data-v-3338238c] {
    position: absolute;
    width: 27.375rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 3rem 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    color: #fff;
    align-items: center;
    border-radius: 0.5rem;
    border: 1px solid rgba(252, 112, 197, 0);
    background: var(
        --Style-1,
        linear-gradient(
            0deg,
            rgba(255, 140, 196, 0.5) 0,
            rgba(255, 140, 196, 0.5) 100%
        ),
        rgba(0, 0, 0, 0.85)
    );
}
.runway-theme-modal h3[data-v-3338238c] {
    color: #fff;
    font-family: Oswald;
    font-size: 1.5rem;
    font-weight: 400;
}
.runway-theme-modal .prp-button[data-v-3338238c] {
    width: 100%;
}
.runway-theme-modal .prp-input[data-v-3338238c] {
    background: #401d32;
    padding: 0 1.5rem;
}
.runway-theme-modal .prp-input[data-v-3338238c] input {
    font-size: 1.25rem;
    padding: 0.75rem 0;
}
.dropdown-wrapper[data-v-16940201] {
    width: 100%;
}
.dropdown-wrapper[data-v-16940201] .dropdown-header {
    background: transparent;
    color: rgba(0, 13, 79, 0.7);
    font-size: 1.5rem;
    font-weight: 700;
    padding: 0;
}
.dropdown-wrapper[data-v-16940201] .dropdown-content {
    background: url(../img/ticket_bg.png);
    border-radius: 0.5rem !important;
    margin-bottom: 2rem;
    box-shadow: 0 0 15px 15px rgba(0, 0, 0, 0.05);
}
.dropdown-wrapper[data-v-16940201] .dropdown-content:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 193, 0.631);
    z-index: -1;
}
.search[data-v-16940201] {
    width: 100%;
    height: 100%;
    padding: 0;
    border: none;
    background-color: transparent;
    color: rgba(0, 13, 79, 0.7);
    font-family: inherit;
    font-size: 1.5rem;
    font-weight: 700;
}
.search[data-v-16940201]:focus {
    outline: none;
}
.title[data-v-16940201] {
    height: 1.625rem;
    display: flex;
    align-items: center;
}
.field[data-v-15fa9e86] {
    display: flex;
    flex-direction: column;
    min-width: 0;
}
.field .label[data-v-15fa9e86] {
    display: flex;
    width: 100%;
    font-family: Inter, "sans-serif";
    font-weight: 600;
    font-size: 1rem;
    color: rgba(0, 0, 0, 0.7);
}
.field .content[data-v-15fa9e86] {
    width: 100%;
    padding: 0;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: Caveat;
    color: rgba(0, 13, 79, 0.7);
    min-height: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.field .content.overflow[data-v-15fa9e86] {
    overflow: visible;
    height: -moz-fit-content;
    height: fit-content;
}
.field .ticket-input[data-v-15fa9e86] {
    padding: 0;
    margin: -0.125rem 0;
    flex: 1;
    color: rgba(0, 13, 79, 0.7);
    font-family: Caveat;
    font-size: 1.5rem;
    font-weight: 700;
    resize: none;
    border: none;
    background: transparent;
}
.field .ticket-input[data-v-15fa9e86]:focus {
    outline: none;
}
.loader-text[data-v-15fa9e86] {
    height: 0.5rem;
    padding: 1rem 0;
    margin: 0;
}
.in[data-v-17aae990] {
    font-family: Inter, sans-serif;
    color: rgba(0, 0, 0, 0.7);
}
.scr[data-v-17aae990] {
    font-family: Caveat, sans-serif;
    color: rgba(0, 13, 79, 0.7);
    font-size: 2rem;
    font-weight: 700;
}
.ticket[data-v-17aae990] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 37.5rem;
    padding: 2rem 1rem;
    box-shadow: 0 0 250px 75px rgba(0, 0, 0, 0.8),
        inset 0 0 24px 4px rgba(0, 0, 0, 0.25);
    border-radius: 0.25rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.ticket .bg[data-v-17aae990] {
    position: absolute;
    border-radius: inherit;
    overflow: hidden;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.ticket .bg[data-v-17aae990]:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 193, 0.631);
}
.ticket .bg img[data-v-17aae990] {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.ticket .header[data-v-17aae990] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.ticket .header p[data-v-17aae990] {
    font-style: italic;
    font-weight: 600;
}
.ticket .ticket-grid[data-v-17aae990] {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    grid-gap: 1rem 0;
}
.ticket .ticket-grid .grid-item[data-v-17aae990] {
    width: 100%;
    position: relative;
}
.ticket .ticket-grid .grid-item[data-v-17aae990]:before {
    content: "";
    position: absolute;
    bottom: -0.75rem;
    width: 100%;
    height: 0.125rem;
    background: rgba(0, 0, 0, 0.9);
}
.ticket .footer[data-v-17aae990] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.ticket .footer .info[data-v-17aae990] {
    font-family: Inter, sans-serif;
    color: rgba(0, 0, 0, 0.7);
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
}
.ticket .footer .row[data-v-17aae990] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.ticket .footer .row .left[data-v-17aae990] {
    color: rgba(0, 0, 0, 0.8);
    font-family: Inter;
    font-size: 0.875rem;
    font-weight: 500;
}
.ticket .footer .row .right[data-v-17aae990] {
    color: rgba(157, 2, 2, 0.7);
    font-family: Inter;
    font-size: 0.875rem;
    font-weight: 500;
}
.ticket .buttons[data-v-17aae990] {
    position: absolute;
    bottom: -1rem;
    right: 0;
    translate: 0 100%;
    display: flex;
    gap: 1rem;
}
.checkbox[data-v-17aae990] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: transparent;
    width: 1rem;
    height: 1rem;
    margin: 0;
    position: relative;
    cursor: pointer;
    border: 1.5px solid rgba(0, 0, 0, 0.7);
    color: rgba(0, 13, 79, 0.7);
}
.checkbox[data-v-17aae990]:disabled {
    pointer-events: none;
    filter: contrast(0.5);
}
.checkbox[data-v-17aae990]:before {
    content: "✓";
    display: block;
    width: 0.75rem;
    height: 0.75rem;
    transition: scale 0.12s ease-in-out;
    position: absolute;
    translate: -20% -150%;
    border-radius: 50%;
    scale: 0;
    font-size: 2rem;
    transform-origin: bottom right;
}
.checkbox[data-v-17aae990]:checked:before {
    scale: 1;
}
.move-up-enter-active[data-v-7dd4346d],
.move-up-leave-active[data-v-7dd4346d] {
    transition: all 0.3s ease-out;
}
.headbag-image[data-v-7dd4346d] {
    width: 100%;
    height: 100%;
}
.headbag-image img[data-v-7dd4346d] {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.move-up-enter-from[data-v-7dd4346d],
.move-up-leave-to[data-v-7dd4346d] {
    transform: translateY(-100%);
    opacity: 0;
}
.book[data-v-2a29dbe0] {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.book .image img[data-v-2a29dbe0] {
    height: max(43.75rem, auto);
    max-width: 80rem;
}
.book .buttons[data-v-2a29dbe0] {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.book-editor[data-v-b9d166fc] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40.5625rem;
    max-height: 50rem;
    padding: 1.5rem;
    border-radius: var(--icon-size, 1.5rem);
    background: rgba(11, 18, 3, 0.85);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    color: #fff;
}
.book-editor h2[data-v-b9d166fc] {
    font-size: 1.5rem;
    font-weight: 700;
}
.book-editor .col[data-v-b9d166fc] {
    width: 100%;
    display: flex;
    gap: 1rem;
}
.book-editor .col[data-v-b9d166fc] > * {
    flex: 1;
}
.book-editor .pages[data-v-b9d166fc] {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 100%;
    overflow: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.book-editor .pages[data-v-b9d166fc]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.book-editor .pages .page[data-v-b9d166fc] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}
.book-editor .pages .page .row[data-v-b9d166fc] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
}
.book-editor .pages .page .row a[data-v-b9d166fc] {
    color: var(--white-75, hsla(0, 0%, 100%, 0.75));
}
.book-editor .buttons[data-v-b9d166fc] {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.slot[data-v-044f72ff] {
    position: relative;
    transition: all 0.3s;
    display: flex;
    border: 0.8px solid var(--White-White-25, hsla(0, 0%, 100%, 0));
    border-radius: 0.125rem;
    background: var(--Black-Black-50, hsla(0, 0%, 7%, 0.5));
    box-shadow: inset 0 0 1rem 0 rgba(62, 197, 255, 0.15);
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
.slot.default[data-v-044f72ff] {
    width: 3.5rem;
    height: 3.5rem;
}
.slot.small[data-v-044f72ff] {
    width: 1.6rem;
    height: 1.6rem;
}
.image-wrapper[data-v-044f72ff] {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -o-object-fit: contain;
    object-fit: contain;
}
.image-wrapper.default[data-v-044f72ff] {
    width: 2.5rem;
    height: 2.5rem;
}
.image-wrapper.small[data-v-044f72ff] {
    width: 1rem;
    height: 1rem;
}
.image-wrapper img[data-v-044f72ff] {
    width: 100%;
    height: 100%;
}
.slot-background[data-v-044f72ff] {
    position: relative;
    isolation: isolate;
}
.slot-background[data-v-044f72ff]:before {
    position: absolute;
    top: calc(var(--imBorderWidth) * -1);
    left: calc(var(--imBorderWidth) * -1);
    width: 100%;
    height: 100%;
    padding: var(--imBorderWidth);
    box-sizing: content-box;
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    background: var(--imBorderBg);
    z-index: -1;
    transition: opacity 0.2s ease, background 0.2s ease;
    border-radius: inherit;
    content: "";
}
.slot-content[data-v-044f72ff] {
    transition: all 0.2s ease-in-out;
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    padding: calc(0.625rem * var(--scale)) calc(0.5rem * var(--scale))
        calc(0.75rem * var(--scale)) calc(0.5rem * var(--scale));
    overflow: hidden;
}
.product[data-v-59e60cda] {
    display: flex;
    padding: 0.5rem;
    align-items: center;
    gap: var(--gap-gap-3, 0.75rem);
    width: 100%;
    justify-content: space-between;
    border-radius: 0.5rem;
}
.product .image[data-v-59e60cda] {
    display: flex;
    width: 3rem;
    height: 3rem;
    padding: 0.25rem;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}
.product .info[data-v-59e60cda] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1 1 0%;
}
.product .info p[data-v-59e60cda] {
    color: var(--White-White, #fff);
    font-family: Geist;
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}
.product .info .numbers[data-v-59e60cda] {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    align-content: flex-start;
    row-gap: 0.5rem;
    align-self: stretch;
    flex-wrap: wrap;
}
.product .info .numbers .quantity[data-v-59e60cda] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    font-size: 0.75rem;
}
.product .info .numbers .price[data-v-59e60cda] {
    color: #fff;
    font-size: 0.75rem;
}
.product .remove[data-v-59e60cda] {
    display: flex;
    align-items: center;
    gap: 0.625rem;
    cursor: pointer;
}
.product .remove svg[data-v-59e60cda] {
    width: 1rem;
    height: 1rem;
}
.product .divider[data-v-59e60cda] {
    width: 100%;
    height: 0.0625rem;
    background: var(--White-White-5, hsla(0, 0%, 100%, 0.05));
}
.cart_view[data-v-09022971] {
    display: flex;
    flex-direction: column;
    width: 15rem;
    gap: 1rem;
    height: 100%;
}
.cart_view .cart_content[data-v-09022971] {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 0.5rem;
    width: 100%;
    height: 100%;
    border-radius: 1rem;
    border: 1px solid hsla(0, 0%, 100%, 0.05);
    background: hsla(0, 0%, 7%, 0.8);
}
.cart_view .cart_content h5[data-v-09022971] {
    color: #fff;
    font-family: Oswald;
    font-size: 1.8rem;
    font-weight: 400;
    text-transform: uppercase;
    margin-bottom: 1.5rem;
}
.cart_view .cart_content .divider[data-v-09022971] {
    height: 0.0625rem;
    width: 100%;
    background: hsla(0, 0%, 100%, 0.05);
}
.cart_view .cart_products[data-v-09022971] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow-y: scroll;
    width: 100%;
    flex-grow: 1;
}
.cart_view .cart_overview[data-v-09022971] {
    gap: 1rem;
}
.cart_view .cart_numbers[data-v-09022971],
.cart_view .cart_overview[data-v-09022971] {
    display: flex;
    flex-direction: column;
    width: 100%;
}
.cart_view .cart_numbers .cart_total[data-v-09022971] {
    display: flex;
    justify-content: space-between;
    width: 100%;
    color: #fff;
    font-family: Oswald;
    font-size: 1.5rem;
    text-transform: uppercase;
}
.cart_view .cart_numbers .cart_items[data-v-09022971] {
    color: hsla(0, 0%, 100%, 0.5);
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 500;
}
.cart_view .cart_button[data-v-09022971] {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 3rem;
    padding: 0.75rem;
    cursor: pointer;
    border-radius: 0.5rem;
    border: 1px solid #87da21;
    background: rgba(135, 218, 33, 0.1);
    transition: background 0.2s;
}
.cart_view .cart_button p[data-v-09022971] {
    color: #fff;
    font-family: Oswald;
    font-size: 1.125rem;
}
.cart_view .cart_button[data-v-09022971]:hover {
    background: rgba(135, 218, 33, 0.2);
}
.cart_view .cart_button[data-v-09022971]:disabled {
    cursor: not-allowed;
    background: rgba(135, 218, 33, 0.05);
}
.cart_view .input_group[data-v-09022971] {
    position: relative;
    display: flex;
    width: 100%;
}
.cart_view .input_group input[data-v-09022971] {
    width: 100%;
    height: 2.75rem;
    padding: 0.5rem 0.75rem;
    color: #fff;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: hsla(0, 0%, 7%, 0.8);
    font-family: Geist;
    font-size: 0.875rem;
}
.cart_view .input_group input[data-v-09022971]::-moz-placeholder {
    color: #777;
    font-weight: 400;
}
.cart_view .input_group input[data-v-09022971]::placeholder {
    color: #777;
    font-weight: 400;
}
.cart_view .input_group .icon[data-v-09022971] {
    position: absolute;
    right: 1rem;
    top: 55%;
    transform: translateY(-50%);
    color: hsla(0, 0%, 100%, 0.5);
}
.product[data-v-13fc49c7] {
    display: flex;
    flex-direction: column;
    padding: var(--gap-gap-3, 0.75rem);
    gap: 1rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.05);
    background: hsla(0, 0%, 100%, 0.02);
    box-sizing: border-box;
}
.product .product-row[data-v-13fc49c7] {
    display: flex;
    align-items: center;
    gap: var(--gap-gap-3, 1rem);
    width: 100%;
}
.product .product-row p[data-v-13fc49c7] {
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 600;
}
.product .info[data-v-13fc49c7] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    flex: 1 1 0%;
}
.product .info .description[data-v-13fc49c7] {
    color: #fff;
    font-size: 0.75rem;
    font-weight: 100;
    opacity: 0.5;
}
.product .numbers[data-v-13fc49c7] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-top: auto;
}
.product .numbers .label[data-v-13fc49c7] {
    color: #fff;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.25rem;
    opacity: 0.5;
    text-transform: uppercase;
}
.product .numbers .price[data-v-13fc49c7] {
    color: #fff;
    font-size: 0.75rem;
}
.product .buttons[data-v-13fc49c7] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    width: 100%;
}
.product .buttons .quantity[data-v-13fc49c7] {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.product .buttons .quantity .button[data-v-13fc49c7] {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.35rem 0.35rem;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    background: hsla(0, 0%, 100%, 0.05);
    cursor: pointer;
}
.product .buttons .quantity .button[data-v-13fc49c7]:hover {
    background: hsla(0, 0%, 100%, 0.1);
}
.product .buttons .quantity .button svg[data-v-13fc49c7] {
    color: #fff;
    width: 1.1rem;
    height: 1.1rem;
}
.product .buttons .quantity input[data-v-13fc49c7] {
    width: 2.5rem;
    padding: 0.6rem 0.75rem;
    text-align: center;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    background: hsla(0, 0%, 7%, 0.8);
    color: #fff;
    -moz-appearance: textfield;
}
.product .buttons .quantity input[data-v-13fc49c7]::-webkit-inner-spin-button,
.product .buttons .quantity input[data-v-13fc49c7]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.product .buttons .button[data-v-13fc49c7] {
    display: flex;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(135, 218, 33, 0.5);
    background: rgba(135, 218, 33, 0.1);
    cursor: pointer;
}
.product .buttons .button[data-v-13fc49c7]:hover {
    background: rgba(135, 218, 33, 0.2);
}
.product .buttons .button p[data-v-13fc49c7] {
    color: #fff;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}
.product_view[data-v-177c696e] {
    display: flex;
    flex-grow: 1;
    padding: 1.5rem 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
    flex: 1 0 0;
    border-radius: 1rem;
    border: 1px solid hsla(0, 0%, 100%, 0.05);
    background: hsla(0, 0%, 7%, 0.8);
    width: 100%;
}
.filters[data-v-177c696e] {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1.5rem;
    width: 100%;
}
.filters .filter-search[data-v-177c696e] {
    flex: 1;
}
.filters .filter-category[data-v-177c696e] {
    width: 14rem;
}
.filters[data-v-177c696e] .dropdown-header {
    background-color: hsla(0, 0%, 7%, 0.8);
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-family: Geist;
    font-size: 0.875rem;
    color: #fff;
}
.filters[data-v-177c696e] .dropdown-header.placeholder {
    color: hsla(0, 0%, 100%, 0.5);
}
.filters[data-v-177c696e] .dropdown-header.noOpen {
    cursor: unset;
}
.filters[data-v-177c696e] .dropdown-header svg,
.filters[data-v-177c696e] .dropdown-header.disabled {
    color: hsla(0, 0%, 100%, 0.5);
}
.filters[data-v-177c696e] .dropdown-content {
    background-color: #121212;
    border-left: 1px solid hsla(0, 0%, 100%, 0.15);
    border-right: 1px solid hsla(0, 0%, 100%, 0.15);
    border-bottom: 1px solid hsla(0, 0%, 100%, 0.15);
    border-bottom-right-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
    padding: 0.5rem 0;
    width: 100%;
    max-height: 50vh;
    overflow-y: auto;
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
}
.filters[data-v-177c696e] .dropdown-content span {
    padding: 0.5rem 1rem;
}
.filters[data-v-177c696e] .dropdown-content:deep(.item) {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: red;
    border-top: 1px solid hsla(0, 0%, 100%, 0.05);
    font-weight: 400;
}
.filter[data-v-177c696e] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.filter p[data-v-177c696e] {
    color: hsla(0, 0%, 100%, 0.5);
    font-family: Geist;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.25rem;
}
.input_group[data-v-177c696e] {
    position: relative;
    display: flex;
    width: 100%;
}
.input_group input[data-v-177c696e] {
    width: 100%;
    height: 2.75rem;
    padding: 0.5rem 0.75rem;
    color: #fff;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.15);
    background: hsla(0, 0%, 7%, 0.8);
    font-family: Geist;
    font-size: 0.875rem;
}
.input_group input[data-v-177c696e]::-moz-placeholder {
    color: #777;
    font-weight: 400;
}
.input_group input[data-v-177c696e]::placeholder {
    color: #777;
    font-weight: 400;
}
.input_group .icon[data-v-177c696e] {
    position: absolute;
    right: 1rem;
    top: 55%;
    transform: translateY(-50%);
    color: hsla(0, 0%, 100%, 0.5);
}
.products[data-v-177c696e] {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(12.5rem, 1fr));
    width: 100%;
    gap: 1.25rem;
    overflow-y: auto;
    border-radius: 0.5rem;
    max-height: 34rem;
}
.product[data-v-cf86f04e] {
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.05);
    background: hsla(0, 0%, 100%, 0.02);
    box-sizing: border-box;
}
.product .product-col[data-v-cf86f04e],
.product[data-v-cf86f04e] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.product .product-col .order-title[data-v-cf86f04e] {
    color: var(--White-White, #fff);
    font-size: 0.875rem;
    font-family: Geist;
    font-weight: 600;
}
.product .products-grid[data-v-cf86f04e] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    gap: 0.5rem;
}
.product .product[data-v-cf86f04e] {
    width: 100%;
    display: flex;
    flex-direction: row;
    padding: 0.5rem 0.75rem;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.5);
    background: hsla(0, 0%, 100%, 0.01);
    transition: all 0.3s ease-in-out;
}
.product .product[data-v-cf86f04e]:hover {
    border-color: hsla(0, 0%, 100%, 0.75);
}
.product .product .info[data-v-cf86f04e] {
    color: var(--White-White, #fff);
    font-size: 0.75rem;
    font-family: Geist, sans-serif;
    font-weight: 400;
    text-align: center;
}
.product .numbers[data-v-cf86f04e] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
.product .numbers .label[data-v-cf86f04e] {
    color: #fff;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.25rem;
    opacity: 0.5;
    text-transform: uppercase;
}
.product .numbers .price[data-v-cf86f04e] {
    color: #fff;
    font-size: 1.5rem;
}
.product .buttons[data-v-cf86f04e] {
    display: flex;
    gap: 1rem;
    align-items: center;
    width: 100%;
}
.product .buttons .button[data-v-cf86f04e] {
    display: flex;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
}
.product .buttons .button.primary[data-v-cf86f04e] {
    border: 1px solid rgba(135, 218, 33, 0.5);
    background: var(--Brand-Brand-10, rgba(135, 218, 33, 0.1));
}
.product .buttons .button.primary[data-v-cf86f04e]:hover {
    background: rgba(135, 218, 33, 0.2);
}
.product .buttons .button.secondary[data-v-cf86f04e] {
    border: 1px solid var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    background: var(--White-White-10, hsla(0, 0%, 100%, 0.1));
}
.product .buttons .button.secondary[data-v-cf86f04e]:hover {
    background: var(--White-White-20, hsla(0, 0%, 100%, 0.2));
}
.product .buttons .button.destructive[data-v-cf86f04e] {
    border: 1px solid rgba(218, 33, 33, 0.5);
    background: rgba(218, 33, 33, 0.1);
}
.product .buttons .button.destructive[data-v-cf86f04e]:hover {
    background: rgba(218, 33, 33, 0.2);
}
.product .buttons .button p[data-v-cf86f04e] {
    color: #fff;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}
.product_view[data-v-63c86488] {
    display: flex;
    padding: 1.5rem 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
    flex: 1 0 0;
    border-radius: 1rem;
    border: 1px solid hsla(0, 0%, 100%, 0.05);
    background: hsla(0, 0%, 7%, 0.8);
    width: 100%;
}
.products[data-v-63c86488] {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 1.25rem;
    max-height: 40rem;
    overflow-y: auto;
}
.pos_wrapper[data-v-534d1000] {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 68rem;
}
.pos_wrapper .container[data-v-534d1000] {
    display: flex;
    width: 76rem;
    height: 54rem;
    padding: 3rem;
    flex-direction: column;
    gap: 1rem;
    border-radius: 1.5rem;
    border: 0.0625rem solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: var(--Black-Black-80, hsla(0, 0%, 7%, 0.8));
}
.pos_wrapper .container .header[data-v-534d1000] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
.pos_wrapper .container .header .title[data-v-534d1000] {
    color: var(--White-White, #fff);
    font-size: 2.5rem;
    font-family: Oswald, sans-serif;
    font-weight: 400;
    text-transform: uppercase;
}
.pos_wrapper .container .button_row[data-v-534d1000] {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1rem 1.5rem;
    border-radius: 1rem;
    border: 0.0625rem solid var(--White-White-5, hsla(0, 0%, 100%, 0.05));
    background: var(--Black-Black-80, hsla(0, 0%, 7%, 0.8));
}
.pos_wrapper .container .button_row .link[data-v-534d1000] {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #fff;
    cursor: pointer;
}
.pos_wrapper .container .button_row .link svg[data-v-534d1000] {
    width: 1.5rem;
    height: 1.5rem;
    color: currentColor;
}
.pos_wrapper .container .button_row .link .link-text[data-v-534d1000] {
    font-size: 1rem;
    font-family: Oswald, sans-serif;
    text-transform: uppercase;
}
.pos_wrapper .container .button_row .link.active[data-v-534d1000] {
    color: var(--Brand-Brand, #87da21);
}
.pos_wrapper .container .button_row .divider[data-v-534d1000] {
    width: 0.0625rem;
    height: 100%;
    background: var(--White-White-10, hsla(0, 0%, 100%, 0.1));
}
.pos_wrapper .container .content[data-v-534d1000] {
    display: flex;
    flex: 1;
    width: 100%;
    gap: 1rem;
}
.pos_wrapper .notif[data-v-534d1000] {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}
.pos_wrapper .notif.success[data-v-534d1000] {
    border: 1px solid #87da21;
    border-radius: 0.5rem;
    background: rgba(135, 218, 33, 0.1);
}
.pos_wrapper .notif.error[data-v-534d1000] {
    border: 1px solid #ad1321;
    border-radius: 0.5rem;
    background: rgba(180, 25, 33, 0.1);
}
.pos_wrapper .notif p[data-v-534d1000] {
    color: #fff;
    font-size: 1rem;
    font-family: Oswald, sans-serif;
    font-weight: 400;
}
.voting-band[data-v-2754963b] {
    width: 28.75rem;
    height: auto;
    display: flex;
    border-top-left-radius: 1.5rem;
    border-top-right-radius: 1.5rem;
    overflow: hidden;
}
.voting-band.reverse[data-v-2754963b] {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 1.5rem;
    border-bottom-left-radius: 1.5rem;
}
.voting-band .voting-stripes[data-v-2754963b] {
    display: flex;
    flex-direction: column;
    flex: 1;
}
.voting-band .voting-stripes div[data-v-2754963b] {
    height: 0.6rem;
}
.voting-band .voting-stripes div[data-v-2754963b]:nth-child(odd) {
    background: #a31a24;
}
.voting-band .voting-stripes div[data-v-2754963b]:nth-child(2n) {
    background: #fff;
}
.voting-band .voting-stripes.reverse[data-v-2754963b] {
    order: 2;
}
.voting-band .voting-stars[data-v-2754963b] {
    display: flex;
    padding: 0.75rem 2.125rem;
    justify-content: center;
    align-items: center;
    gap: 0.94rem;
    flex: 1;
    background: #0c4b81;
    color: #fff;
}
.voting-band .voting-stars svg[data-v-2754963b] {
    width: 1.4rem;
    height: 1.4rem;
}
.header[data-v-01d42c04] {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}
.header .title[data-v-01d42c04] {
    font-size: 3rem;
    line-height: 3rem;
    text-align: center;
}
.header .subtitle[data-v-01d42c04],
.header .title[data-v-01d42c04] {
    color: #0c4b81;
    text-transform: uppercase;
    font-family: Oswald;
    font-style: normal;
    font-weight: 400;
}
.header .subtitle[data-v-01d42c04] {
    font-size: 1rem;
    line-height: normal;
}
.item[data-v-11640533] {
    width: 100%;
    display: flex;
    gap: 1rem;
    align-items: center;
    color: #0c4b81;
    font-family: Oswald;
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #0c4b81;
    cursor: pointer;
}
.item .square-box[data-v-11640533] {
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #0c4b81;
    background: #fff;
}
.item .square-box svg[data-v-11640533] {
    width: 1rem;
    height: 1rem;
    color: #0c4b81;
}
.item.selected[data-v-11640533] {
    color: #a31a24;
}
.item.selected .square-box[data-v-11640533] {
    border-color: #a31a24;
    background: rgba(163, 26, 36, 0.2);
}
.item[data-v-11640533]:last-child {
    border-bottom: 0;
    padding-bottom: 0;
}
.list[data-v-500e26a6] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    align-self: stretch;
    max-height: 20rem;
    overflow-y: auto;
    margin-right: -1rem;
    margin-top: -1rem;
    padding: 1rem 1rem 1rem 0;
    margin-bottom: -1rem;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.list[data-v-500e26a6]::-webkit-scrollbar-thumb {
    background: #a31a24;
    border-radius: 1rem;
}
.list[data-v-500e26a6]::-webkit-scrollbar-track {
    margin-top: 1rem;
    margin-bottom: 1rem;
    background: rgba(0, 0, 0, 0.1);
}
.button[data-v-18a8c327] {
    display: flex;
    padding: 1rem;
    flex-direction: column;
    align-items: center;
    gap: 0.625rem;
    align-self: stretch;
    background: #a31a24;
    text-transform: uppercase;
    color: #fff;
    font-family: Oswald;
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1rem;
    cursor: pointer;
    transition: background 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.button[data-v-18a8c327]:hover {
    background: rgba(163, 26, 36, 0.7);
}
.voting-wrapper[data-v-6eb6ddec] {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.voting-card[data-v-6eb6ddec] {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    background: hsla(0, 0%, 100%, 0.95);
    border-radius: 1.5rem;
}
.voting-body[data-v-6eb6ddec] {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 28.75rem;
    padding: 0 2rem;
    gap: 2.5rem;
    overflow: hidden;
}
.voting-body .voted[data-v-6eb6ddec] {
    color: #0c4b81;
    text-transform: uppercase;
    font-family: Oswald;
    font-size: 3rem;
    font-style: normal;
    font-weight: 400;
    line-height: 3rem;
    text-align: center;
}
@keyframes flash-67c1c3a4 {
    0%,
    to {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}
.map-wrapper[data-v-67c1c3a4] {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.75rem;
}
.map-wrapper .map[data-v-67c1c3a4] {
    flex: 1;
    width: 100%;
    border-radius: 0.5rem;
}
.map-wrapper .map[data-v-67c1c3a4] .blip {
    width: -moz-max-content !important;
    width: max-content !important;
    height: auto !important;
    display: inline-flex;
    padding: 0.5rem 0.375rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    border-radius: 0.125rem;
    color: #fff;
    text-align: right;
    font-family: Oswald;
    font-size: 0.875rem;
    line-height: 0.875rem;
}
.map-wrapper .map[data-v-67c1c3a4] .blip.flash {
    animation: flash-67c1c3a4 0.75s infinite;
}
.map-wrapper .map[data-v-67c1c3a4] .blip.illegal {
    border: 1px solid red;
    background: linear-gradient(
            92deg,
            hsla(0, 100%, 70%, 0.2),
            rgba(255, 0, 0, 0.2) 85.41%
        ),
        rgba(0, 0, 0, 0.85);
}
.map-wrapper .map[data-v-67c1c3a4] .blip.unauthorized {
    border: 1px solid #4b4b4b;
    background: rgba(0, 0, 0, 0.85);
}
.map-wrapper .map[data-v-67c1c3a4] .blip.authorized {
    border: 1px solid #01a1e2;
    background: linear-gradient(
            92deg,
            rgba(135, 206, 235, 0.2),
            rgba(1, 161, 226, 0.2) 85.41%
        ),
        rgba(0, 0, 0, 0.85);
}
.wrapper[data-v-330c5f7e] {
    display: flex;
    padding: 1.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.875rem;
    align-self: stretch;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.2);
    background: linear-gradient(
            0deg,
            rgba(135, 206, 235, 0.05),
            rgba(135, 206, 235, 0.05)
        ),
        rgba(0, 0, 0, 0.8);
    transition: background 0.5s ease;
}
@keyframes flash-330c5f7e {
    0%,
    to {
        background: linear-gradient(
                0deg,
                rgba(135, 206, 235, 0.1),
                rgba(135, 206, 235, 0.1)
            ),
            rgba(0, 0, 0, 0.8);
    }
    50% {
        background: linear-gradient(
                0deg,
                rgba(135, 206, 235, 0.15),
                rgba(135, 206, 235, 0.15)
            ),
            rgba(0, 0, 0, 0.8);
    }
}
.wrapper.hailed[data-v-330c5f7e] {
    animation: flash-330c5f7e 1s ease-in-out 5;
    animation-fill-mode: forwards;
}
.wrapper.unauthorized[data-v-330c5f7e] {
    border: 1px solid hsla(0, 0%, 100%, 0.2);
    background: #130606;
}
.wrapper.unknown[data-v-330c5f7e] {
    border: 1px solid hsla(0, 0%, 100%, 0.2);
    background: rgba(0, 0, 0, 0.8);
}
.wrapper.blink[data-v-330c5f7e] {
    box-shadow: 0 0 0 0.125rem #87da21;
}
.wrapper .body[data-v-330c5f7e],
.wrapper .heading[data-v-330c5f7e] {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Geist;
}
.wrapper .body div[data-v-330c5f7e],
.wrapper .heading div[data-v-330c5f7e] {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 7rem;
    text-align: left;
    color: #fff;
    font-size: 0.875rem;
    font-weight: 700;
}
.wrapper .body div svg[data-v-330c5f7e],
.wrapper .heading div svg[data-v-330c5f7e] {
    width: 1rem;
    height: 1rem;
}
.wrapper .heading span[data-v-330c5f7e] {
    color: #959595;
    font-size: 0.75rem;
}
.wrapper .body span[data-v-330c5f7e] {
    color: #fff;
    font-size: 1.125rem;
    font-weight: 700;
}
.wrapper .actions[data-v-330c5f7e] {
    display: flex;
    gap: 0.75rem;
}
.wrapper .actions .action-button[data-v-330c5f7e] {
    display: flex;
    height: 2rem;
    padding: 0.25rem 0.625rem;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: transparent;
    color: #fff;
    font-family: Geist;
    font-size: 0.75rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}
.wrapper .actions .action-button svg[data-v-330c5f7e] {
    width: 1rem;
    height: 1rem;
}
.wrapper
    .actions
    .action-button[data-v-330c5f7e]:hover:not(
        .wrapper .actions .action-button:disabled
    ) {
    background: #fff;
    color: #010101;
}
.wrapper .actions .action-button[data-v-330c5f7e]:disabled {
    color: hsla(0, 0%, 100%, 0.25);
    border: 1px solid hsla(0, 0%, 100%, 0.5);
    cursor: not-allowed;
}
.filters-wrapper[data-v-24bd9cda] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.75rem;
}
.filters-wrapper .filter-button[data-v-24bd9cda] {
    display: flex;
    padding: 0.375rem 0.75rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.3125rem;
    flex: 1 0 0;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.4);
    background: hsla(0, 0%, 100%, 0.1);
    transition: all 0.2s ease-in-out;
    color: #fff;
    font-family: Oswald;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
}
.filters-wrapper .filter-button.selected[data-v-24bd9cda],
.filters-wrapper .filter-button[data-v-24bd9cda]:hover {
    background: #fff;
    color: #010101;
}
.aircrafts-list[data-v-38e6aa22] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    overflow-y: auto;
    margin-top: -1rem;
    padding: 1rem 1px 1rem 1px;
    -webkit-mask: linear-gradient(
        180deg,
        transparent 0,
        #fff 1rem,
        #fff calc(100% - 1rem),
        transparent
    );
}
.aircrafts-list[data-v-38e6aa22]::-webkit-scrollbar {
    display: none;
}
.broadcast[data-v-7ddc666c] {
    display: flex;
    width: 100%;
    padding: 0.375rem 0.75rem;
    justify-content: center;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
    border-radius: 0.25rem;
    border: 1px solid hsla(0, 0%, 100%, 0.4);
    background: hsla(0, 0%, 100%, 0.1);
    color: #fff;
    font-family: Oswald;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}
.broadcast svg[data-v-7ddc666c] {
    width: 1.2rem;
    height: 1.2rem;
}
.broadcast[data-v-7ddc666c]:hover {
    background: #fff;
    color: #010101;
}
.broadcast.active[data-v-7ddc666c] {
    border: 1px solid hsla(0, 0%, 100%, 0.2);
    background: #130606;
}
.broadcast.active[data-v-7ddc666c]:hover {
    background: #fff;
    color: #010101;
}
.header[data-v-749561be] {
    display: flex;
    align-items: center;
}
.header .label[data-v-749561be] {
    color: #fff;
    font-size: 2.25rem;
    font-weight: 400;
}
.header .error[data-v-749561be] {
    color: crimson;
}
.footer[data-v-0158cd6d] {
    display: flex;
    align-items: center;
    padding: 0 2.5rem;
}
.footer .label[data-v-0158cd6d] {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #fff;
    font-size: 1.25rem;
    font-weight: 400;
}
.footer .key[data-v-0158cd6d] {
    height: 2rem;
    min-width: 2rem;
    display: inline-block;
    text-align: center;
    padding: 0 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid var(--White-White-50, hsla(0, 0%, 100%, 0.5));
    background: hsla(0, 0%, 100%, 0.15);
    line-height: 170%;
    vertical-align: center;
    margin: 0 0.25rem;
}
.wrapper[data-v-c2149e76] {
    perspective: var(--perspective);
    overflow: visible;
    display: flex;
    width: 100%;
    padding: 0.75rem 5rem;
    gap: 3rem;
}
.atc-grid[data-v-c2149e76] {
    display: grid;
    grid-template-columns: 1fr 0.225fr;
    grid-template-rows: auto 1fr auto;
    padding: 2rem 5rem;
    -moz-column-gap: 3.5rem;
    column-gap: 3.5rem;
    row-gap: 1.5rem;
    height: 100vh;
    overflow: hidden;
    perspective: var(--perspective);
}
.atc-grid.bg[data-v-c2149e76] {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.85));
}
.atc-panel[data-v-c2149e76] {
    overflow: auto;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    transform: rotateY(-8deg);
    transform-origin: right top;
}
.dropdown-wrapper[data-v-dd0bc2e0] {
    width: 100%;
}
.dropdown-wrapper[data-v-dd0bc2e0] .dropdown-header {
    background: transparent;
    color: rgba(0, 13, 79, 0.7);
    font-size: 1.5rem;
    font-weight: 700;
    padding: 0;
}
.dropdown-wrapper[data-v-dd0bc2e0] .dropdown-content {
    background: url(../img/permit_bg.png);
    border-radius: 0.5rem !important;
    margin-bottom: 2rem;
    box-shadow: 0 0 15px 15px rgba(0, 0, 0, 0.05);
}
.dropdown-wrapper[data-v-dd0bc2e0] .dropdown-content:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 193, 0.631);
    z-index: -1;
}
.search[data-v-dd0bc2e0] {
    width: 100%;
    height: 100%;
    padding: 0;
    border: none;
    background-color: transparent;
    color: rgba(0, 13, 79, 0.7);
    font-family: inherit;
    font-size: 1.5rem;
    font-weight: 700;
}
.search[data-v-dd0bc2e0]:focus {
    outline: none;
}
.title[data-v-dd0bc2e0] {
    height: 1.625rem;
    display: flex;
    align-items: center;
}
.field[data-v-8441fa84] {
    display: flex;
    flex-direction: column;
    min-width: 0;
}
.field .label[data-v-8441fa84] {
    display: flex;
    width: 100%;
    font-family: Inter, "sans-serif";
    font-weight: 600;
    font-size: 1rem;
    color: rgba(0, 0, 0, 0.7);
}
.field .content[data-v-8441fa84] {
    width: 100%;
    padding: 0;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: Caveat;
    color: rgba(0, 13, 79, 0.7);
    min-height: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.field .content.textarea[data-v-8441fa84] {
    height: 8rem;
    white-space: pre-wrap;
    align-items: flex-start;
}
.field .content.overflow[data-v-8441fa84] {
    overflow: visible;
    height: -moz-fit-content;
    height: fit-content;
}
.field .permit-input[data-v-8441fa84] {
    padding: 0;
    margin: -0.125rem 0;
    flex: 1;
    color: rgba(0, 13, 79, 0.7);
    font-family: Caveat;
    font-size: 1.5rem;
    font-weight: 700;
    border: none;
    background: transparent;
    resize: none;
}
.field .permit-input[data-v-8441fa84]:focus {
    outline: none;
}
.loader-text[data-v-8441fa84] {
    height: 0.5rem;
    padding: 1rem 0;
    margin: 0;
}
.in[data-v-5812dfc7] {
    font-family: Inter, sans-serif;
    color: rgba(0, 0, 0, 0.7);
}
.scr[data-v-5812dfc7] {
    font-family: Caveat, sans-serif;
    color: rgba(0, 13, 79, 0.7);
    font-size: 2rem;
    font-weight: 700;
}
.permit[data-v-5812dfc7] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 37.5rem;
    padding: 2rem 1rem;
    box-shadow: 0 0 250px 75px rgba(0, 0, 0, 0.8),
        inset 0 0 24px 4px rgba(0, 0, 0, 0.25);
    border-radius: 0.25rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.permit .bg[data-v-5812dfc7] {
    position: absolute;
    border-radius: inherit;
    overflow: hidden;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.permit .bg[data-v-5812dfc7]:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 193, 0.631);
}
.permit .bg img[data-v-5812dfc7] {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.permit .header[data-v-5812dfc7] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.permit .header p[data-v-5812dfc7] {
    font-style: italic;
    font-weight: 600;
}
.permit .permit-grid[data-v-5812dfc7] {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    grid-gap: 1rem 0;
}
.permit .permit-grid .grid-item[data-v-5812dfc7] {
    width: 100%;
    position: relative;
}
.permit .permit-grid .grid-item[data-v-5812dfc7]:before {
    content: "";
    position: absolute;
    bottom: -0.75rem;
    width: 100%;
    height: 0.125rem;
    background: rgba(0, 0, 0, 0.9);
}
.permit .footer[data-v-5812dfc7] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.permit .footer .info[data-v-5812dfc7] {
    font-family: Inter, sans-serif;
    color: rgba(0, 0, 0, 0.7);
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
}
.permit .footer .row[data-v-5812dfc7] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.permit .footer .row .left[data-v-5812dfc7] {
    color: rgba(0, 0, 0, 0.8);
    font-family: Inter;
    font-size: 0.875rem;
    font-weight: 500;
}
.permit .footer .row .right[data-v-5812dfc7] {
    color: rgba(157, 2, 2, 0.7);
    font-family: Inter;
    font-size: 0.875rem;
    font-weight: 500;
}
.permit .buttons[data-v-5812dfc7] {
    position: absolute;
    bottom: -1rem;
    right: 0;
    translate: 0 100%;
    display: flex;
    gap: 1rem;
}
.checkbox[data-v-5812dfc7] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: transparent;
    width: 1rem;
    height: 1rem;
    margin: 0;
    position: relative;
    cursor: pointer;
    border: 1.5px solid rgba(0, 0, 0, 0.7);
    color: rgba(0, 13, 79, 0.7);
}
.checkbox[data-v-5812dfc7]:disabled {
    pointer-events: none;
    filter: contrast(0.5);
}
.checkbox[data-v-5812dfc7]:before {
    content: "✓";
    display: block;
    width: 0.75rem;
    height: 0.75rem;
    transition: scale 0.12s ease-in-out;
    position: absolute;
    translate: -20% -150%;
    border-radius: 50%;
    scale: 0;
    font-size: 2rem;
    transform-origin: bottom right;
}
.checkbox[data-v-5812dfc7]:checked:before {
    scale: 1;
}
.font-option[data-v-649adbe4] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    width: -moz-max-content;
    width: max-content;
    color: #fff;
}
.font-option .font-option-label[data-v-649adbe4] {
    color: #818181;
    leading-trim: both;
    text-edge: cap;
    font-family: Geist;
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1rem;
}
.font-styling-toggle[data-v-22138636] {
    padding: 0.375rem;
    border-radius: 0.125rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    background: linear-gradient(
            0deg,
            rgba(0, 0, 0, 0.5) 0,
            rgba(0, 0, 0, 0.5) 100%
        ),
        var(--tailwind-colors-base-transparent, hsla(0, 0%, 100%, 0));
    cursor: pointer;
}
.font-styling-toggle[data-v-22138636] svg {
    width: 1rem;
    height: 1rem;
    color: #fff;
}
.font-styling-toggle.active[data-v-22138636] {
    background: linear-gradient(0deg, #87da21 0, #87da21 100%),
        var(--tailwind-colors-base-transparent, hsla(0, 0%, 100%, 0));
}
.scene-editor-colorpicker-slider-wrapper[data-v-3197caaf] {
    position: relative;
    overflow: visible;
    height: -moz-fit-content;
    height: fit-content;
}
.scene-editor-colorpicker-slider-wrapper.disabled[data-v-3197caaf] {
    opacity: 0.4;
}
.scene-editor-colorpicker-slider[data-v-3197caaf] {
    width: 100%;
    height: 0.5rem;
    border: none;
    border-radius: 2rem;
    -webkit-appearance: none;
}
.scene-editor-colorpicker-slider[data-v-3197caaf]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    -webkit-transition: filter 0.1s ease;
    transition: filter 0.1s ease;
    background: var(--selectedColor);
}
.scene-editor-color-picker[data-v-1636a6f8] {
    position: relative;
}
.mock-input[data-v-1636a6f8] {
    width: 100%;
    border-radius: var(--XXS, 0.5rem);
    background: var(--input-bg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 13rem;
    padding: 0.5rem 1rem;
    color: #fff;
    cursor: pointer;
}
.mock-input .colorSq[data-v-1636a6f8] {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.125rem;
    background: var(--picker-color);
}
.picker-menu[data-v-1636a6f8] {
    position: absolute;
    top: 0;
    left: -0.5rem;
    width: 100%;
    border-radius: var(--XXS, 0.5rem);
    translate: -100% 0;
    padding: 1rem;
    z-index: 10;
    isolation: isolate;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.picker-menu .history[data-v-1636a6f8] {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-gap: 0.5rem;
}
.picker-menu .history .history-entry[data-v-1636a6f8] {
    width: 100%;
    aspect-ratio: 1;
    border-radius: 0.125rem;
    background: var(--entry);
    border: 1px solid hsla(0, 0%, 100%, 0.2);
    cursor: pointer;
}
.picker-menu[data-v-1636a6f8]:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    width: 20rem;
    height: 20rem;
    background: radial-gradient(
        circle at center,
        rgba(0, 0, 0, 0.2) 0,
        rgba(0, 0, 0, 0.2) 50%,
        transparent 60%
    );
    z-index: -1;
}
.picker-menu .canvas-wrapper[data-v-1636a6f8] {
    height: 11.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
}
.picker-menu .canvas-wrapper canvas[data-v-1636a6f8] {
    width: 11.75rem;
    height: 11.75rem;
    background: transparent;
    box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.251);
    border-radius: 50%;
}
.rotate-in-enter-active[data-v-1636a6f8],
.rotate-in-leave-active[data-v-1636a6f8],
.rotate-in-move[data-v-1636a6f8] {
    transition: scale 0.2s ease;
    transform-origin: top center;
}
.rotate-in-enter-from[data-v-1636a6f8],
.rotate-in-leave-to[data-v-1636a6f8] {
    scale: 1 0;
}
.font-options-modal[data-v-7f70e61f] {
    position: absolute;
    top: -6rem;
    display: flex;
    padding: 1rem;
    gap: 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: #0c0c0c;
}
.font-preview[data-v-7f70e61f] {
    color: #fff;
    leading-trim: both;
    text-edge: cap;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5rem;
}
.font-preview.option[data-v-7f70e61f] {
    font-size: 0.75rem;
}
[data-v-7f70e61f] .prp-input {
    min-width: 2rem;
    padding: 0 0.5rem;
}
[data-v-7f70e61f] .prp-input input {
    padding: 0.375rem 0;
}
.font-styling[data-v-7f70e61f] {
    display: flex;
    gap: 0.5rem;
}
.font-family-dropdown[data-v-7f70e61f],
.font-size-dropdown[data-v-7f70e61f] {
    --radius: 0.125rem;
    --dropdown-header-bg-color: rgba(0, 0, 0, 0.5);
    --padding: 0.375rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    width: 4rem;
    --dropdown-content-bg-color: #060606;
}
.font-family-dropdown[data-v-7f70e61f] .dropdown-header,
.font-size-dropdown[data-v-7f70e61f] .dropdown-header {
    min-width: 4rem;
}
.font-family-dropdown[data-v-7f70e61f] .dropdown-content,
.font-size-dropdown[data-v-7f70e61f] .dropdown-content {
    max-height: 5rem;
}
.font-family-dropdown[data-v-7f70e61f] {
    width: 11rem;
}
[data-v-7f70e61f] .scene-editor-color-picker {
    --XXS: 0.125rem;
    --input-bg: rgba(0, 0, 0, 0.5);
    border: 1px solid hsla(0, 0%, 100%, 0.1);
}
[data-v-7f70e61f] .scene-editor-color-picker .mock-input {
    padding: 0.375rem;
    min-width: 7rem;
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5rem;
}
[data-v-7f70e61f] .scene-editor-color-picker .mock-input .colorSq {
    width: 1rem;
    height: 1rem;
    border-radius: 100rem;
}
[data-v-7f70e61f] .scene-editor-color-picker .picker-menu {
    translate: 90% -50%;
    width: calc(100% + 5rem);
}
.image-options-modal[data-v-34bb5fc7] {
    position: absolute;
    top: -6rem;
    display: flex;
    padding: 1rem;
    gap: 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: #0c0c0c;
}
.font-preview[data-v-34bb5fc7] {
    color: #fff;
    leading-trim: both;
    text-edge: cap;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5rem;
}
.font-preview.option[data-v-34bb5fc7] {
    font-size: 0.75rem;
}
.metadata-options-modal[data-v-53b12926] {
    position: absolute;
    top: -6rem;
    display: flex;
    padding: 1rem;
    gap: 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: #0c0c0c;
}
.font-preview[data-v-53b12926] {
    color: #fff;
    leading-trim: both;
    text-edge: cap;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5rem;
}
.font-preview.option[data-v-53b12926] {
    font-size: 0.75rem;
}
.book-title-input[data-v-53b12926] {
    --input-bg: rgba(0, 0, 0, 0.5);
    --XXS: 0.125rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    padding: 0 0.375rem;
}
.book-title-input[data-v-53b12926] input {
    padding: 0.375rem;
    line-height: 1.5rem;
}
[data-v-53b12926] .scene-editor-color-picker {
    --XXS: 0.125rem;
    --input-bg: rgba(0, 0, 0, 0.5);
    border: 1px solid hsla(0, 0%, 100%, 0.1);
}
[data-v-53b12926] .scene-editor-color-picker .mock-input {
    padding: 0.375rem;
    min-width: 7rem;
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5rem;
}
[data-v-53b12926] .scene-editor-color-picker .mock-input .colorSq {
    width: 1rem;
    height: 1rem;
    border-radius: 100rem;
}
[data-v-53b12926] .scene-editor-color-picker .picker-menu {
    translate: 90% -50%;
    width: calc(100% + 5rem);
}
.image-add-modal[data-v-3be0a06c] {
    position: absolute;
    top: -6rem;
    display: flex;
    padding: 1rem;
    gap: 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: #0c0c0c;
}
.image-url-input[data-v-3be0a06c] {
    --input-bg: rgba(0, 0, 0, 0.5);
    --XXS: 0.125rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    padding: 0 0.375rem;
}
.image-url-input[data-v-3be0a06c] input {
    padding: 0.375rem;
    line-height: 1.5rem;
}
.draw-options-modal[data-v-7951da0c] {
    position: absolute;
    top: -6rem;
    display: flex;
    padding: 1rem;
    gap: 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    background: #0c0c0c;
}
.tool-container[data-v-7951da0c] {
    display: flex;
    gap: 0.5rem;
}
.font-preview[data-v-7951da0c] {
    color: #fff;
    leading-trim: both;
    text-edge: cap;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5rem;
}
.font-preview.option[data-v-7951da0c] {
    font-size: 0.75rem;
}
.draw-size-dropdown[data-v-7951da0c] {
    --radius: 0.125rem;
    --dropdown-header-bg-color: rgba(0, 0, 0, 0.5);
    --padding: 0.375rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    width: 4rem;
    --dropdown-content-bg-color: #060606;
}
.draw-size-dropdown[data-v-7951da0c] .dropdown-header {
    min-width: 4rem;
}
.draw-size-dropdown[data-v-7951da0c] .dropdown-content {
    max-height: 5rem;
}
[data-v-7951da0c] .scene-editor-color-picker {
    --XXS: 0.125rem;
    --input-bg: rgba(0, 0, 0, 0.5);
    border: 1px solid hsla(0, 0%, 100%, 0.1);
}
[data-v-7951da0c] .scene-editor-color-picker .mock-input {
    padding: 0.375rem;
    min-width: 7rem;
    color: #fff;
    font-family: Geist;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5rem;
}
[data-v-7951da0c] .scene-editor-color-picker .mock-input .colorSq {
    width: 1rem;
    height: 1rem;
    border-radius: 100rem;
}
[data-v-7951da0c] .scene-editor-color-picker .picker-menu {
    translate: 130% -50%;
    width: calc(100% + 5rem);
}
.draw-options-modal-buttons[data-v-7951da0c] {
    display: flex;
    gap: 0.5rem;
    height: 100%;
}
.draw-options-modal-buttons button[data-v-7951da0c] {
    padding: 0.375rem;
    border-radius: 0.125rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    background: linear-gradient(
            0deg,
            rgba(0, 0, 0, 0.5) 0,
            rgba(0, 0, 0, 0.5) 100%
        ),
        var(--tailwind-colors-base-transparent, hsla(0, 0%, 100%, 0));
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}
.draw-options-modal-buttons button svg[data-v-7951da0c] {
    width: 1rem;
    height: 1rem;
}
.draw-options-modal-buttons button.save[data-v-7951da0c]:hover {
    background: #87da21;
}
.draw-options-modal-buttons button.clear[data-v-7951da0c]:hover {
    background: #da2121;
}
.opacity-input[data-v-7951da0c] {
    --input-bg: rgba(0, 0, 0, 0.5);
    --XXS: 0.125rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    padding: 0 0.375rem;
    min-width: 4rem;
    width: 4rem;
}
.opacity-input[data-v-7951da0c] input {
    padding: 0.375rem;
    line-height: 1.5rem;
}
.toolbar[data-v-43d39d29] {
    height: 2.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}
.toolbar .info-text[data-v-43d39d29] {
    color: #989898;
    font-size: 1rem;
    text-align: center;
}
.change-title-modal-wrapper[data-v-43d39d29],
.color-modal-wrapper[data-v-43d39d29],
.font-color-modal-wrapper[data-v-43d39d29],
.font-modal-wrapper[data-v-43d39d29],
.image-modal-wrapper[data-v-43d39d29] {
    width: 10rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    position: relative;
}
.change-title-modal-wrapper .button[data-v-43d39d29],
.color-modal-wrapper .button[data-v-43d39d29],
.font-color-modal-wrapper .button[data-v-43d39d29],
.font-modal-wrapper .button[data-v-43d39d29],
.image-modal-wrapper .button[data-v-43d39d29] {
    color: #989898;
    background-color: var(--input-bg);
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.change-title-modal-wrapper .button[data-v-43d39d29]:hover,
.color-modal-wrapper .button[data-v-43d39d29]:hover,
.font-color-modal-wrapper .button[data-v-43d39d29]:hover,
.font-modal-wrapper .button[data-v-43d39d29]:hover,
.image-modal-wrapper .button[data-v-43d39d29]:hover {
    color: #fff;
}
.change-title-modal-wrapper span[data-v-43d39d29],
.color-modal-wrapper span[data-v-43d39d29],
.font-color-modal-wrapper span[data-v-43d39d29],
.font-modal-wrapper span[data-v-43d39d29],
.image-modal-wrapper span[data-v-43d39d29] {
    color: #989898;
}
.change-title-modal-wrapper span.limit[data-v-43d39d29],
.color-modal-wrapper span.limit[data-v-43d39d29],
.font-color-modal-wrapper span.limit[data-v-43d39d29],
.font-modal-wrapper span.limit[data-v-43d39d29],
.image-modal-wrapper span.limit[data-v-43d39d29] {
    color: red;
}
.color-modal-wrapper[data-v-43d39d29],
.font-color-modal-wrapper[data-v-43d39d29] {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 4rem;
}
.color-modal-wrapper[data-v-43d39d29] .picker-menu,
.font-color-modal-wrapper[data-v-43d39d29] .picker-menu {
    translate: 0 -100%;
    left: 0;
    top: 0.8rem;
}
.font-modal-wrapper[data-v-43d39d29] {
    width: auto;
}
.font-button[data-v-43d39d29] {
    background: var(--input-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    cursor: pointer;
    padding: 0.5rem 2.5rem;
    color: #fff;
}
.font-button.font-1[data-v-43d39d29] {
    font-family: Merriweather, serif;
}
.font-button.font-2[data-v-43d39d29] {
    font-family: Covered By Your Grace, cursive;
}
.font-button.font-3[data-v-43d39d29] {
    font-family: "Source Serif 4", serif;
}
.buttons[data-v-43d39d29] {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
}
.buttons.modal-open[data-v-43d39d29] {
    margin-bottom: -4rem;
}
.buttons .button[data-v-43d39d29] {
    width: 1.5rem;
    height: 1.5rem;
    color: #989898;
    transition: color 0.3s ease;
    cursor: pointer;
}
.buttons .button[data-v-43d39d29]:hover {
    color: #fff;
}
.buttons .button.active[data-v-43d39d29] {
    color: #87da21;
}
.editor {
    height: 100%;
    width: 100%;
}
.editor[data-v-0be4b73d] {
    height: 100%;
    width: 100%;
    font-family: Covered By Your Grace;
    font-size: 1.25rem;
    overflow-y: hidden;
}
.editor .ProseMirror[data-v-0be4b73d] {
    overflow-y: hidden;
}
.page-content-wrapper[data-v-0be4b73d] {
    position: absolute;
    cursor: default;
    overflow: hidden;
    height: auto;
}
.page-content-wrapper.selected[data-v-0be4b73d] {
    z-index: 100;
    cursor: move;
}
.page-content-wrapper.editing[data-v-0be4b73d] {
    cursor: text;
}
.page-content[data-v-0be4b73d] {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
    line-height: 30px;
    font-size: 18px;
    outline: none;
    white-space: pre-wrap;
    word-break: break-word;
    color: #4c4c4c;
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 400;
    min-height: 100%;
    max-height: 100%;
    overflow-y: hidden;
    box-sizing: border-box;
}
.resize-handles[data-v-0be4b73d] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border: 2px dotted #87da21;
}
.resize-handles .resize-handle[data-v-0be4b73d] {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #87da21;
    border: 1px solid #fff;
    border-radius: 2px;
    pointer-events: all;
}
.resize-handles .resize-handle.e[data-v-0be4b73d] {
    top: 50%;
    right: -5px;
    transform: translateY(-50%);
    cursor: e-resize;
    width: 10px;
    height: 10px;
}
.resize-handles .resize-handle.w[data-v-0be4b73d] {
    top: 50%;
    left: -5px;
    transform: translateY(-50%);
    cursor: w-resize;
    width: 10px;
    height: 10px;
}
.bubble-menu[data-v-0be4b73d] {
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 0.7rem;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    padding: 0.2rem;
}
.bubble-menu button[data-v-0be4b73d] {
    background-color: unset;
}
.bubble-menu button[data-v-0be4b73d]:hover {
    background-color: #ccc;
}
.bubble-menu button.is-active[data-v-0be4b73d] {
    background-color: red;
}
.bubble-menu button.is-active[data-v-0be4b73d]:hover {
    background-color: #ccc;
}
.image-wrapper[data-v-422154d4] {
    position: absolute;
    display: inline-block;
    margin: 0;
    z-index: 2;
    box-sizing: content-box;
}
.image-wrapper.editMode[data-v-422154d4] {
    cursor: move;
}
.page-image[data-v-422154d4] {
    max-width: 100%;
    max-height: 100%;
    display: block;
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    pointer-events: none;
    -o-object-fit: cover;
    object-fit: cover;
}
.page-image.mask[data-v-422154d4] {
    -webkit-mask-image: url(../img/image_cover.svg);
    mask-image: url(../img/image_cover.svg);
    -webkit-mask-size: cover;
    mask-size: cover;
    -webkit-mask-composite: source-out;
    mask-composite: subtract;
}
.image-wrapper.selected[data-v-422154d4] {
    outline: 2px solid #87da21;
    outline-offset: 2px;
    z-index: 3;
}
.resize-handles[data-v-422154d4] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
.resize-handle[data-v-422154d4] {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #87da21;
    border: 1px solid #fff;
    border-radius: 2px;
    pointer-events: all;
}
.resize-handle.n[data-v-422154d4] {
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    cursor: n-resize;
    width: 10px;
    height: 10px;
}
.resize-handle.ne[data-v-422154d4] {
    top: -5px;
    right: -5px;
    cursor: ne-resize;
}
.resize-handle.e[data-v-422154d4] {
    top: 50%;
    right: -5px;
    transform: translateY(-50%);
    cursor: e-resize;
    width: 10px;
    height: 10px;
}
.resize-handle.se[data-v-422154d4] {
    bottom: -5px;
    right: -5px;
    cursor: se-resize;
}
.resize-handle.s[data-v-422154d4] {
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    cursor: s-resize;
    width: 10px;
    height: 10px;
}
.resize-handle.sw[data-v-422154d4] {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize;
}
.resize-handle.w[data-v-422154d4] {
    top: 50%;
    left: -5px;
    transform: translateY(-50%);
    cursor: w-resize;
    width: 10px;
    height: 10px;
}
.resize-handle.nw[data-v-422154d4] {
    top: -5px;
    left: -5px;
    cursor: nw-resize;
}
#draw-canvas[data-v-82ec442e] {
    --zIndex: -1;
    z-index: var(--zIndex);
}
#draw-canvas[data-v-82ec442e],
.page[data-v-9d0c3444] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
.page[data-v-9d0c3444] {
    background: #fff;
    overflow: visible;
    right: 0;
    bottom: 0;
}
.page.remove-page-mode[data-v-9d0c3444]:hover {
    border: 1px solid #87da21;
}
.page.--right[data-v-9d0c3444] {
    border-radius: 0 0.5rem 0.5rem 0;
    background: repeating-linear-gradient(
            180deg,
            transparent,
            transparent 1.75rem,
            hsla(0, 0%, 59%, 0.3) calc(1.75rem + 1px),
            transparent 1.875rem
        ),
        linear-gradient(
            90deg,
            #b9b9b9,
            #c9c9c9 2%,
            #e0e0e0 6%,
            #f1f1f1 10%,
            #fbfbfb 14%,
            #fff 19%,
            #fff
        );
    box-shadow: 0 1rem 0.75rem 0 rgba(0, 0, 0, 0.5);
}
.page.--right .select-page-overlay[data-v-9d0c3444] {
    border-radius: 0 0.5rem 0.5rem 0;
}
.page.--left[data-v-9d0c3444] {
    border-radius: 0.5rem 0 0 0.5rem;
    background: repeating-linear-gradient(
            180deg,
            transparent,
            transparent 28px,
            hsla(0, 0%, 59%, 0.3) 29px,
            transparent 30px
        ),
        linear-gradient(
            90deg,
            #fff,
            #fff 62%,
            #fff 72%,
            #fcfcfc 83%,
            #f5f5f5 88%,
            #e9e9e9 92%,
            #d7d7d7 95%,
            silver 98%,
            #a3a3a3
        );
    box-shadow: 0 16px 12px 0 rgba(0, 0, 0, 0.5);
}
.page.--left .select-page-overlay[data-v-9d0c3444] {
    border-radius: 0.5rem 0 0 0.5rem;
}
.page .select-page-overlay[data-v-9d0c3444] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    transition: border 0.3s ease;
    cursor: pointer;
}
.page .select-page-overlay[data-v-9d0c3444]:hover {
    border: 2px solid #87da21;
}
.page #draw-canvas[data-v-9d0c3444] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.wrapper[data-v-12fa2151] {
    position: fixed;
    pointer-events: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
.container[data-v-12fa2151] {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    gap: 2rem;
    transition: opacity 0.5s ease;
}
.container .page-count[data-v-12fa2151] {
    text-align: center;
    color: #818181;
    font-family: Geist;
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 400;
    line-height: 0.75rem;
    text-transform: uppercase;
    height: 0.75rem;
}
.container .page-count span[data-v-12fa2151] {
    color: #fff;
}
.container .page-count span.current-page[data-v-12fa2151] {
    padding: 0.375rem;
    border-radius: 0.125rem;
    border: 1px solid hsla(0, 0%, 100%, 0.1);
    background: rgba(0, 0, 0, 0.5);
}
.container .notebook-wrapper[data-v-12fa2151] {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.25rem;
}
.container .notebook-wrapper .notebook-container[data-v-12fa2151] {
    width: 37.75rem;
    height: 51rem;
    position: relative;
    perspective: 2502px;
    perspective-origin: 50%;
    transition: transform 0.5s ease-in;
}
.container .notebook-wrapper .notebook-container .cover[data-v-12fa2151] {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transform-style: preserve-3d;
    transform-origin: left;
    transition: transform 0.5s ease-in;
}
.container .notebook-wrapper .notebook-container .cover svg[data-v-12fa2151] {
    width: 100%;
    height: 100%;
}
.container .notebook-wrapper .notebook-container .right[data-v-12fa2151] {
    position: relative;
    height: 100%;
    box-shadow: 0 5px 0 var(--5ba9b0ed), 5px 0 0 var(--5ba9b0ed),
        inset -5px 0 0 var(--5ba9b0ed);
    border-radius: 0 1.8rem 1.3rem 0;
    background-color: var(--5ba9b0ed);
}
.container .notebook-wrapper .notebook-container .right svg[data-v-12fa2151] {
    width: 100%;
    height: 100%;
}
.container .notebook-wrapper .notebook-container .back[data-v-12fa2151],
.container .notebook-wrapper .notebook-container .right[data-v-12fa2151] {
    visibility: hidden;
}
.container .notebook-wrapper .notebook-container .front[data-v-12fa2151] {
    z-index: 2;
    backface-visibility: hidden;
}
.container .notebook-wrapper .notebook-container .back[data-v-12fa2151] {
    z-index: 1;
    box-shadow: 0 5px 0 var(--5ba9b0ed);
    border-radius: 1.8rem 1.8rem 1.8rem 0;
    background-color: var(--5ba9b0ed);
}
.container .notebook-wrapper .notebook-container .flipped[data-v-12fa2151] {
    transform: rotateY(-180deg);
}
.container
    .notebook-wrapper
    .notebook-container
    .book-container[data-v-12fa2151] {
    position: absolute;
    top: 1rem;
    translate: -50% 0;
    width: 73.25rem;
    height: 48.75rem;
}
.container .notebook-wrapper .notebook-container #book[data-v-12fa2151] {
    position: relative;
    top: 0;
    left: 0;
    z-index: 2;
}
.container .notebook-wrapper .arrow-button[data-v-12fa2151] {
    position: absolute;
    top: 50%;
    left: calc(-50% - 1rem);
    translate: -100% -50%;
    width: 1.5rem;
    height: 1.5rem;
    color: hsla(0, 0%, 100%, 0.5);
    transition: color, opacity 0.3s ease;
    cursor: pointer;
    opacity: 0;
}
.container .notebook-wrapper .arrow-button.right-arrow[data-v-12fa2151] {
    left: unset;
    right: calc(-50% - 1rem);
    translate: 100% -50%;
}
.container .notebook-wrapper .arrow-button svg[data-v-12fa2151] {
    width: 100%;
    height: 100%;
}
.container .notebook-wrapper .arrow-button[data-v-12fa2151]:hover {
    color: #fff;
}
.container .notebook-wrapper .book-title[data-v-12fa2151] {
    width: 90.5%;
    position: absolute;
    top: 50%;
    left: 50%;
    height: 25rem;
    text-align: center;
    padding: 1rem;
    overflow: hidden;
    word-wrap: anywhere;
    display: flex;
    justify-content: center;
    align-items: center;
}
.container .notebook-wrapper .book-title.front[data-v-12fa2151] {
    width: 618px;
    transform: translate(-50%, -50%);
}
.container .notebook-wrapper .book-title span[data-v-12fa2151] {
    font-family: Covered By Your Grace;
    font-size: 3.5rem;
    font-style: normal;
    font-weight: 400;
    line-height: 3.5rem;
    text-align: center;
    display: inline-block;
    letter-spacing: 2px;
    background: linear-gradient(90deg, #b7b7b7, #fffbe6 40%, #f8f8f8);
    -webkit-background-clip: text;
    background-clip: text;
    color: #d0d0d0;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.35), 0 1px 0 #fffbe6,
        0 0.5px 0 #bfa100;
    filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.25));
    opacity: 0.95;
}
.field[data-v-1b1c8b67] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 0;
}
.field .label[data-v-1b1c8b67] {
    font-family: Geist;
    display: flex;
    width: 100%;
    font-size: 0.875rem;
    text-transform: uppercase;
    color: hsla(0, 0%, 100%, 0.65);
}
.field.dark .label[data-v-1b1c8b67] {
    color: hsla(0, 0%, 100%, 0.5);
}
.field .content[data-v-1b1c8b67] {
    font-weight: 400;
}
.loader-text[data-v-1b1c8b67] {
    height: 0.5rem;
    padding: 1rem 0;
    margin: 0;
}
.dropdown-wrapper[data-v-1e535721] {
    color: #fff;
}
.search[data-v-1e535721] {
    width: 100%;
    height: 100%;
    padding: 0;
    border: none;
    background-color: transparent;
    color: #fff;
    font-family: inherit;
    font-size: 1rem;
}
.search[data-v-1e535721]:focus {
    outline: none;
}
.title[data-v-1e535721] {
    height: 1.625rem;
    display: flex;
    align-items: center;
}
.submenu[data-v-11cd3d95] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 15.375rem;
}
.dropdown-wrapper[data-v-11cd3d95] {
    color: #fff;
}
.font-preview[data-v-11cd3d95] {
    height: 1rem;
}
.submenu[data-v-7110a814] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 15.375rem;
}
.dropdown-wrapper[data-v-7110a814] {
    color: #fff;
}
.row[data-v-7110a814] {
    position: relative;
    display: flex;
    gap: 0.5rem;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    font-size: 1rem;
}
.row[data-v-7110a814] > * {
    flex-shrink: 0;
}
.row .backgrounds[data-v-7110a814] {
    position: absolute;
    left: -1rem;
    top: 0;
    translate: -100% 0;
    width: 16rem;
    height: 20rem;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    grid-gap: 0.5rem;
    overflow: auto;
    padding-right: 0.5rem;
}
.row .backgrounds .bg[data-v-7110a814] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    padding: 0.375rem 0.5rem;
    color: #fff;
    font-size: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid hsla(0, 0%, 100%, 0.2);
    background: rgba(11, 18, 3, 0.85);
    cursor: pointer;
    transition: background 0.2s ease-in-out;
}
.row .backgrounds .bg[data-v-7110a814]:hover {
    background: rgba(11, 18, 3, 0.95);
}
.row .backgrounds .bg .image[data-v-7110a814] {
    width: 6.18rem;
    height: 3.89rem;
    border-radius: 0.5rem;
    overflow: hidden;
}
.row .backgrounds .bg .image img[data-v-7110a814] {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
.slide-enter-active[data-v-7110a814],
.slide-leave-active[data-v-7110a814] {
    transition: scale 0.2s ease;
    transform-origin: top center;
}
.slide-enter-from[data-v-7110a814],
.slide-leave-to[data-v-7110a814] {
    scale: 1 0;
}
.submenu[data-v-65f3f3ec] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 15.375rem;
}
.dropdown-wrapper[data-v-65f3f3ec] {
    color: #fff;
}
.scene-menu-wrapper[data-v-99b693c0] {
    position: absolute;
    top: 50%;
    right: 2.5rem;
    translate: 0 -50%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    transform-origin: right center;
    transform: rotateY(-8deg);
    isolation: isolate;
}
.scene-menu-wrapper .bg[data-v-99b693c0] {
    position: absolute;
    top: 50%;
    right: 0;
    translate: 50% -50%;
    height: 40rem;
    width: 40rem;
    background: radial-gradient(
        circle at 50% 50%,
        rgba(0, 0, 0, 0.35) 0,
        transparent 50%
    );
    z-index: -1;
}
.scene-menu-wrapper .navbar[data-v-99b693c0] {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.scene-menu-wrapper .navbar .item[data-v-99b693c0] {
    color: hsla(0, 0%, 100%, 0.5);
    font-family: Oswald;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 0;
    cursor: pointer;
    transition: color 0.2s linear;
    height: 1.5rem;
}
.scene-menu-wrapper .navbar .item[data-v-99b693c0]:hover {
    color: #e5e7eb;
}
.scene-menu-wrapper .navbar .item.active[data-v-99b693c0] {
    padding: 0 0.375rem;
    border-radius: 0.25rem;
    border: 1px solid var(--Brand-Brand, #87da21);
    background: var(--Brand-Brand-25, rgba(135, 218, 33, 0.25));
    color: var(--gray-200, #e5e7eb);
    cursor: auto;
}
.buttons[data-v-99b693c0] {
    display: flex;
    gap: 1rem;
}
.scene-preset[data-v-19b9c1c6] {
    display: flex;
    width: 11.625rem;
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    justify-content: center;
    gap: 0.5rem;
    border-radius: 0.125rem;
    background: #0b1203;
    box-shadow: inset 0 0 16px 0 rgba(35, 35, 35, 0.15);
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(45deg, #646464, transparent);
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: var(--White-White, #fff);
    font-family: Geist;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.scene-preset[data-v-19b9c1c6]:hover {
    background: #0a0a0a;
}
.scene-history[data-v-1c3247c0] {
    display: flex;
    width: 11.625rem;
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    justify-content: center;
    gap: 0.5rem;
    border-radius: 0.125rem;
    background: #0b1203;
    box-shadow: inset 0 0 16px 0 rgba(35, 35, 35, 0.15);
    --imBorderWidth: 1px;
    --imBorderBg: linear-gradient(45deg, #646464, transparent);
    color: var(--White-White, #fff);
    font-family: Geist;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    cursor: pointer;
    transition: background-color 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.scene-history[data-v-1c3247c0]:hover {
    background: #0a0a0a;
}
.scene-history .timeago[data-v-1c3247c0] {
    color: #7e7e7e;
    font-size: 0.75rem;
    font-weight: 400;
    text-transform: uppercase;
}
.scene-presets-history[data-v-74b6111e] {
    position: absolute;
    top: 50%;
    left: 2.5rem;
    translate: 0 -50%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    transform-origin: right center;
    transform: rotateY(-8deg);
}
.scene-presets-history .navbar[data-v-74b6111e] {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.scene-presets-history .navbar .item[data-v-74b6111e] {
    color: hsla(0, 0%, 100%, 0.5);
    font-family: Oswald;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 0;
    cursor: pointer;
    transition: color 0.2s linear;
    height: 1.5rem;
}
.scene-presets-history .navbar .item[data-v-74b6111e]:hover {
    color: #e5e7eb;
}
.scene-presets-history .navbar .item.active[data-v-74b6111e] {
    padding: 0 0.375rem;
    border-radius: 0.25rem;
    border: 1px solid var(--Brand-Brand, #87da21);
    background: var(--Brand-Brand-25, rgba(135, 218, 33, 0.25));
    color: var(--gray-200, #e5e7eb);
    cursor: auto;
}
.submenu[data-v-74b6111e] {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 15.375rem;
}
.rotate-in-enter-active[data-v-5b49605b],
.rotate-in-leave-active[data-v-5b49605b],
.rotate-in-move[data-v-5b49605b] {
    transform-origin: bottom center;
    transition: all 0.5s ease;
}
.rotate-in-enter-from[data-v-5b49605b],
.rotate-in-leave-to[data-v-5b49605b] {
    opacity: 0;
    transform: rotateX(90deg) translateY(100%);
}
.rotate-in-leave-active[data-v-5b49605b] {
    position: absolute;
}
.image-wrap[data-v-8976ef56] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 1rem 1rem 6rem;
    background: #fff;
}
.image[data-v-8976ef56] {
    width: 25rem;
    aspect-ratio: 1;
}
.image img[data-v-8976ef56] {
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    height: 100%;
    filter: grayscale(0.5) blur(1px) contrast(1.2) sepia(1);
    -webkit-mask: radial-gradient(#000 20%, rgba(0, 0, 0, 0.95));
}
.badge[data-v-5855a8dc] {
    --text-color: #fafafa;
    --ring-color: #525252;
    --ring-color-50: rgba(82, 82, 82, 0.5);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    border: 1px solid;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
    font-size: 0.75rem;
    font-weight: 500;
    width: -moz-fit-content;
    width: fit-content;
    white-space: nowrap;
    flex-shrink: 0;
    gap: 0.25rem;
    transition-property: color, box-shadow;
    overflow: hidden;
    color: var(--text-color);
}
.badge[data-v-5855a8dc]:hover {
    background-color: #262626;
    color: var(--text-color);
}
.badge[data-v-5855a8dc]:visible {
    border-color: var(--ring-color);
    box-shadow: 0 0 0 3px var(--ring-color-50);
}
.badge.default[data-v-5855a8dc] {
    background-color: hsla(0, 0%, 100%, 0.051);
    border-color: #fff;
}
.badge.warning[data-v-5855a8dc] {
    background-color: rgba(255, 255, 0, 0.051);
    border-color: #ff0;
}
.badge.success[data-v-5855a8dc] {
    background-color: rgba(135, 218, 33, 0.051);
    border-color: #87da21;
}
table[data-v-0f49fd3a] {
    --text-color: #fafafa;
    --muted: #262626;
    --muted-50: rgba(38, 38, 38, 0.5);
    --muted-foreground: #a1a1a1;
    width: 100%;
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
    color: var(--text-color);
}
thead[data-v-0f49fd3a] {
    height: 2.5rem;
}
thead.sticky[data-v-0f49fd3a] {
    z-index: 10;
    position: sticky;
    top: 0;
    background-color: var(--muted);
}
tr[data-v-0f49fd3a] {
    border-bottom-width: 1px;
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
    border-bottom: 1px solid var(--muted);
}
tr.empty[data-v-0f49fd3a],
tr[data-v-0f49fd3a]:last-child {
    border-bottom: 0;
}
tbody[data-v-0f49fd3a] {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
}
[data-v-0f49fd3a] th {
    padding-inline: 0.5rem;
    vertical-align: middle;
    text-align: left;
    font-weight: 500;
    color: var(--muted-foreground);
    font-family: Geist;
    font-size: 0.875rem;
}
[data-v-0f49fd3a] td {
    padding: 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
}
.loader-button[data-v-db621038] {
    min-width: 6rem;
}
.loader[data-v-db621038] {
    border-radius: 0.5rem;
}
.icon[data-v-db621038] {
    width: 1rem;
    height: 1rem;
}
.prp-button[data-v-db621038] {
    font-family: var(--font-font-sans, Geist);
    font-size: var(--text-xs-font-size, 0.75rem);
    font-style: normal;
    font-weight: var(--font-weight-medium, 500);
    line-height: var(--text-xs-line-height, 1rem);
    gap: 0.5rem;
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    --Dark-Blue---15: #ffffff2f;
}
.container[data-v-5934c822] {
    max-width: 45rem !important;
    height: -moz-max-content !important;
    height: max-content !important;
    --dropdown-header-bg-color: #262626;
    --dropdown-content-bg-color: #262626;
    --dropdown-text-color: #fafafa;
}
.header[data-v-5934c822] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.header h1[data-v-5934c822] {
    color: var(--White-White, #fff);
    font-family: Oswald;
    font-size: 2rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: uppercase;
}
.content[data-v-5934c822] {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 2rem;
}
.control[data-v-5934c822] {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.control-label[data-v-5934c822] {
    color: hsla(0, 0%, 100%, 0.5);
    font-family: Geist;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem;
}
.price[data-v-5934c822] {
    color: var(--green-active);
    font-family: Oswald;
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.process-label[data-v-5934c822] {
    font-weight: 400;
    font-size: 1.25rem;
    font-family: Oswald;
    color: var(--primary);
}
.process-label.error[data-v-5934c822] {
    color: crimson;
}
.container[data-v-11bbf3fe] {
    flex: 1;
    max-width: 70rem;
    height: 40rem;
    padding: 3rem;
    border-radius: 1.5rem;
    border: 1px solid var(--White-White-25, hsla(0, 0%, 100%, 0.25));
    background: var(--Black-Black-80, hsla(0, 0%, 7%, 0.8));
    display: flex;
    flex-direction: column;
    gap: 2rem;
    color: var(--White-White, #fff);
    overflow: hidden;
    --Dark-Blue---15: #ffffff2f;
}
.header[data-v-11bbf3fe] {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.header h1[data-v-11bbf3fe] {
    color: var(--White-White, #fff);
    font-family: Oswald;
    font-size: 2rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: uppercase;
}
.content[data-v-11bbf3fe] {
    flex: 1;
    overflow: auto;
}
.wrapper[data-v-919555a2] {
    flex: 1;
    font-family: Geist;
}
.field[data-v-2ee376fc],
.wrapper[data-v-919555a2] {
    display: flex;
    justify-content: center;
    align-items: center;
}
.field[data-v-2ee376fc] {
    padding: 0.375rem 0.75rem;
    gap: 0.625rem;
    border-radius: 0.5rem;
}
.field.normal[data-v-2ee376fc] {
    background: var(--Black-Black-50, hsla(0, 0%, 7%, 0.5));
}
.field.warning[data-v-2ee376fc] {
    background: linear-gradient(
            0deg,
            rgba(255, 255, 0, 0.2) 0,
            rgba(255, 255, 0, 0.2) 100%
        ),
        var(--Black-Black-50, hsla(0, 0%, 7%, 0.5));
}
[data-v-2ee376fc] .icon svg {
    width: 1rem;
    height: 1rem;
    color: #87da21;
}
.content[data-v-2ee376fc] {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 0.25rem;
}
.label[data-v-2ee376fc] {
    color: var(--White-White-50, hsla(0, 0%, 100%, 0.502));
    font-size: 0.625rem;
    text-transform: uppercase;
}
.label[data-v-2ee376fc],
.value[data-v-2ee376fc] {
    font-family: Geist;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}
.value[data-v-2ee376fc] {
    color: var(--White-White, #fff);
    font-size: 0.875rem;
}
.loader-text[data-v-2ee376fc] {
    margin: unset;
}
.flightschool[data-v-f4fd4804] {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 1rem;
    padding: 2rem;
}
