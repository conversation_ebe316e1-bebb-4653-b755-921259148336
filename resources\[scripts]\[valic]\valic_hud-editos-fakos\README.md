# 🎮 Valic HUD - Advanced FiveM HUD System

![Valic HUD Banner](https://via.placeholder.com/800x200/8B5CF6/FFFFFF?text=Valic+HUD+-+Advanced+FiveM+HUD+System)

## 📋 Popis

Valic HUD je pokročilý HUD systém pro FiveM servery s rozsáhlými možnostmi přizpůsobení. Poskytuje moderní a intuitivní uživatelské rozhraní s podporou pro různé herní mechaniky a interakce.

## ✨ Funkce

### 🎯 Hlavní HUD
- **Zdraví a brnění** - Vizuální indikátory s animacemi
- **Hlad a žízeň** - Systém potřeb s barevným kódováním
- **Stres** - Dynamický stres systém
- **Peníze** - Zobrazení hotovosti a bankovního účtu
- **Zbraně** - Informace o aktuální zbrani a munici
- **Čas a počasí** - Hern<PERSON> čas a aktuální počasí

### 🚗 Vozidlový HUD
- **Rychloměr** - MPH/KMH s RPM indikátorem
- **Palivo** - Ukazatel paliva s varováním
- **Zdraví vozidla** - Motor a karoserie
- **Bezpečnostní pás** - Systém s varováním
- **Světla** - Indikátory světel a blinkrů
- **Převodovka** - Zobrazení aktuálního rychlostního stupně

### 🔧 Interakce
- **Interakční menu** - Kontextové akce
- **Progress bary** - Animované progress bary s podporou animací
- **Notifikace** - Pokročilý notifikační systém
- **Input dialogy** - Různé typy vstupních dialogů
- **Potvrzovací dialogy** - Confirm/cancel dialogy

### 🎨 Přizpůsobení
- **Presety** - Přednastavené konfigurace (Default, Legacy)
- **Pozice elementů** - Přizpůsobitelné pozice všech HUD elementů
- **Styly** - Různé vizuální styly
- **Barvy** - Přizpůsobitelné barevné schéma

## 📦 Instalace

### Požadavky
- **QBCore Framework**
- **ox_lib**
- **oxmysql**
- **MySQL databáze**

### Kroky instalace

1. **Stáhněte a rozbalte** script do složky `resources/[scripts]/[valic]/`

2. **Přidejte do server.cfg:**
```cfg
ensure valic_hud-editos-fakos
```

3. **Databáze** - Script automaticky vytvoří potřebné tabulky při prvním spuštění

4. **Restartujte server**

## ⚙️ Konfigurace

### config.lua

```lua
-- Zbraně bez zobrazení munice
HudConfig = {
    ExcludedWeapons = {
        [`WEAPON_SPEEDRADAR`] = true,
        [`WEAPON_PETROLCAN`] = true,
        -- Přidejte další zbraně...
    },
    ForceHideAmmoWeapons = {
        [`WEAPON_GPSLAUNCHER`] = true,
        -- Zbraně s úplně skrytou municí
    }
}

-- Presety HUD
Presets = {
    DEFAULT = {
        Label = "Default",
        Settings = {
            mapFormat = "circle",
            barsLocation = "horizontal",
            speedometerStyle = "new"
            -- Další nastavení...
        }
    }
}
```

### Lokalizace

Podporované jazyky:
- **Angličtina** (en.json)
- **Švédština** (sv.json)

Přidání nového jazyka:
1. Vytvořte nový soubor v `locales/`
2. Přidejte do `fxmanifest.lua`
3. Použijte `lib.locale()` v config.lua

## 🎮 Ovládání

### Klávesové zkratky
- **F1** - Zobrazit interakční menu
- **F2** - Přepnout HUD
- **LALT** (držet) - Zobrazit ID hráčů
- **B** - Bezpečnostní pás
- **G** - Zapnout/vypnout motor

### Příkazy
- `/togglehud` - Přepnout viditelnost HUD
- `/resetui` - Resetovat UI
- `/huddebug` - Debug režim
- `/seatbelt` - Bezpečnostní pás
- `/engine` - Motor vozidla
- `/seat [číslo]` - Přesednout na sedadlo

## 🔌 API a Exporty

### Client-side exporty

```lua
-- HUD ovládání
exports['valic_hud-editos-fakos']:toggleHUD(visible)
exports['valic_hud-editos-fakos']:isHudVisible()
exports['valic_hud-editos-fakos']:getHudSettings()

-- Notifikace
exports['valic_hud-editos-fakos']:notify(message, type, duration)
exports['valic_hud-editos-fakos']:advancedNotify(data)

-- Progress bary
exports['valic_hud-editos-fakos']:showProgress(data)
exports['valic_hud-editos-fakos']:hideProgress()

-- Input dialogy
exports['valic_hud-editos-fakos']:showInput(data)
exports['valic_hud-editos-fakos']:showTextInput(header, placeholder)
exports['valic_hud-editos-fakos']:showNumberInput(header, min, max)

-- Interakce
exports['valic_hud-editos-fakos']:showInteractionMenu()
exports['valic_hud-editos-fakos']:addCustomInteraction(id, data)
```

### Server-side exporty

```lua
-- Nastavení hráče
exports['valic_hud-editos-fakos']:getPlayerSettings(src)
exports['valic_hud-editos-fakos']:setPlayerSettings(src, settings)
exports['valic_hud-editos-fakos']:getPlayerData(src)
```

## 🎨 UI Komponenty

### Notifikace
```lua
TriggerEvent('valic_hud:client:advancedNotify', {
    title = 'Nadpis',
    message = 'Zpráva',
    type = 'success', -- success, error, warning, info, primary
    duration = 5000,
    icon = 'fas fa-check',
    actions = {
        {
            label = 'Akce',
            action = 'event_name'
        }
    }
})
```

### Progress Bar
```lua
exports['valic_hud-editos-fakos']:showProgress({
    label = 'Načítání...',
    duration = 5000,
    canCancel = true,
    animation = {
        dict = 'anim_dict',
        anim = 'anim_name'
    },
    prop = {
        model = 'prop_model',
        bone = 60309,
        pos = {x = 0.0, y = 0.0, z = 0.0},
        rot = {x = 0.0, y = 0.0, z = 0.0}
    }
})
```

## 🐛 Řešení problémů

### Časté problémy

1. **HUD se nezobrazuje**
   - Zkontrolujte konzoli pro chyby
   - Ověřte, že jsou všechny závislosti nainstalované
   - Restartujte resource

2. **Databázové chyby**
   - Zkontrolujte připojení k MySQL
   - Ověřte oprávnění databáze

3. **UI nefunguje**
   - Zkontrolujte, že jsou UI soubory správně nahrané
   - Použijte `/resetui` příkaz

## 📞 Podpora

- **Discord:** [Váš Discord Server]
- **GitHub:** [Repository Link]
- **Email:** [Váš Email]

## 📄 Licence

Tento projekt je licencován pod MIT licencí. Viz LICENSE soubor pro více informací.

## 🤝 Přispívání

Příspěvky jsou vítány! Prosím:
1. Forkněte repository
2. Vytvořte feature branch
3. Commitněte změny
4. Pushněte do branch
5. Otevřete Pull Request

## 📝 Changelog

### v1.0.0
- Počáteční release
- Základní HUD funkcionalita
- Vozidlový HUD
- Notifikační systém
- Progress bary
- Interakční menu

---

**Vytvořeno s ❤️ pro FiveM komunitu**
