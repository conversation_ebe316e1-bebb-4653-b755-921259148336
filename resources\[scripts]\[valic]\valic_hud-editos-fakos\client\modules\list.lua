-- Valic HUD - List Module
local listCallback = nil

function ShowList(data)
    listCallback = data.callback
    SendNUIMessage({
        action = 'showList',
        data = data
    })
    SetNuiFocus(true, true)
end

RegisterNUICallback('listSelection', function(data, cb)
    SetNuiFocus(false, false)
    if listCallback then
        listCallback(data.selected)
        listCallback = nil
    end
    cb('ok')
end)

exports('showList', ShowList)
