-- Valic HUD - Confirm Module
local confirmCallback = nil

function ShowConfirm(data)
    confirmCallback = data.callback
    SendNUIMessage({
        action = 'showConfirm',
        data = data
    })
    SetNuiFocus(true, true)
end

RegisterNUICallback('confirmResult', function(data, cb)
    SetNuiFocus(false, false)
    if confirmCallback then
        confirmCallback(data.confirmed)
        confirmCallback = nil
    end
    cb('ok')
end)

exports('showConfirm', ShowConfirm)
