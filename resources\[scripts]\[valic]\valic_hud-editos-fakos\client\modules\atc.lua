-- Valic HUD - ATC Module
local QBCore = exports['qb-core']:GetCoreObject()

local aircraftAlerts = {}
local lastAlertTime = {}

RegisterNetEvent('valic_hud:client:aircraftAlert', function(data)
    local aircraftId = data.aircraftId
    local currentTime = GetGameTimer()

    -- Check cooldown
    if lastAlertTime[aircraftId] and (currentTime - lastAlertTime[aircraftId]) < (ATC.AircraftAlertTimeoutSeconds * 1000) then
        return
    end

    lastAlertTime[aircraftId] = currentTime

    -- Show alert
    exports['valic_hud-editos-fakos']:advancedNotify({
        title = 'ATC Alert',
        message = data.message,
        type = 'warning',
        duration = 8000,
        icon = 'fas fa-plane',
        sound = {
            name = 'CONFIRM_BEEP',
            set = 'HUD_MINI_GAME_SOUNDSET'
        }
    })
end)