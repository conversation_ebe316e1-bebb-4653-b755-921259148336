{"UNKNOWN": "Unknown", "KEYBIND_SHOW_INTERACTION_MENU": "Hud - Show Interaction Menu", "KEYBIND_TOGGLE_HUD": "Hud - Toggle HUD", "KEYBIND_HUD_SHOW_IDS": "Hud - Show IDs (Hold)", "VEHICLE_ACTIONS_LABEL": "Vehicle Actions", "GIVE_KEYS_INTERACTION_LABEL": "<PERSON>", "NO_ONE_TO_GIVE_KEYS": "There is no one to give keys to.", "GAVE_KEYS": "You gave keys to the vehicle.", "COULDNT_GIVE_KEYS": "You couldn't give keys to the vehicle.", "SAVED_HUD_SETTINGS": "You saved your HUD settings.", "GPS_ACTIVATED": "GPS activated", "GPS_DISABLED": "GPS disabled", "ALREADY_DOING_ACTION": "Already Doing An Action", "USE_PAYPHONE_LABEL": "Use Payphone", "CALL_ENDED": "Call Ended", "FIELDS_NOT_COMPLETE": "The following fields were not completed: %s", "INVALID_SID": "Invalid State ID", "DATE_LC": "date", "NAME_LC": "name", "SID_LC": "state ID", "VIOLATION_LC": "violation", "REGISTRATION_LC": "vehicle registration", "FINE_LC": "fine", "COURT_DATE_LC": "court date", "YOU_NEED_TO_BE_DRIVER_ENGINE": "You need to be the driver to toggle the ignition.", "VEH_DOESNT_HAVE_THIS_SEAT": "This vehicle doesn't have this seat.", "SEAT_OCCUPIED": "This seat is occupied.", "RESETS_UI_COMMAND": "Resets UI", "DOING_THIS_TOO_MUCH": "You're Trying To Do This Too Much, Stop.", "BOOK_IS_EMPTY": "This book is empty!", "CREATE_BOOK_COMMAND_HELP": "[Admin] Create new storybook", "LACK_PERMISSION": "You lack the required permissions", "INVALID_IMG_LINK": "Invalid image link", "INVALID_IMG_LINK_PAGE": "Invalid image link on page ", "STORYBOOK_ITEM_LABEL": "Book", "FAILED_TO_ADD_ITEM": "Failed to add item!", "HUD_PRESET_DEFAULT_LABEL": "<PERSON><PERSON><PERSON>", "HUD_PRESET_LEGACY_LABEL": "Legacy (Old)", "WELCOME_TO_PRP": "Welcome to ProdigyRP!", "TIPS_TITLE": "Some more information", "APARTMENT_TIP_TITLE": "Your new apartment!", "APARTMENT_TIP_TEXT": "In your F1 Menu - or by targeting the tablet on your apartment wall with your 'third eye' pressing alt, you'll be able to decorate your brand new home with our intuitive housing system. Also - in your welcome package we've given you some complimentary furniture to get started!", "PHONE_TIP_TITLE": "Your phone", "PHONE_TIP_TEXT": "In your inventory you'll find your phone, if you right click your phone you will be able to open it and place your sim card inside. In the top corner of your inventory you'll find the utility tab which will allow you to equip your phone.", "CIVILIAN_WORK_TIP_TITLE": "Civilian Work", "CIVILIAN_WORK_TIP_TEXT": "Pick your choice of civilian work by opening your phone and downloading the Labor or Zoomer Driver apps. Both apps contain loads of honest work through out the map that all of progression and skill tree benefits!", "SKILL_TREE_TIP_TITLE": "Skill Trees", "SKILL_TREE_TIP_TEXT": "In the radial menu you will find a tab called 'Perk Menu'. In this menu you will find a whole host of civilian or criminal skill tree's that you can level by completing the associated activity type. You can level one skill tree at a time, but please take note that there is a daily XP cap to prevent excessive grinding.", "NO_ARENA_CATS_TIP_TITLE": "No arena cats!", "NO_ARENA_CATS_TIP_TEXT": "We have a new rule for 2.0 to maintain authenticity with clothing to prevent players from dressing as toxic rp environment subcultures or to obvious FiveM Arena callouts like combinations of bowl cut hair, surgical gloves, cat ears, and goggles.", "TICKET_RULEBREAKS_TIP_TITLE": "Open a ticket for rulebreaks", "TICKET_RULEBREAKS_TIP_TEXT": "Open a ticket on discord for rule breaks - don't bring it in character! Breaking character is one of the most severe rulebreaks on the server and will overshadow most complaints.", "LOCKPICKS_TIP_TITLE": "Lockpicks", "LOCKPICKS_TIP_TEXT": "It's much easier to hotwire or unlock a stolen car using a screwdriver or a lock pick. You can try to hotwire local cars, but it is MUCH harder.", "ANIMATIONS_CLOTHING_TIP_TITLE": "Animations and Clothing", "ANIMATIONS_CLOTHING_TIP_TEXT": "In the radial menu you can find visualized emotes that are placeable in the world that you can favorite or set any key combination to utilize on the fly. In addition we have a menu to unequip clothing pieces and giving them to your friends... or robbers. Pay attention to this feature because if you manage to get your hand on some rare sneakers or clothing you might have to hand those over in a stick up with a rival gang or professional criminal.", "FIELDS_NOT_COMPLETED": "The following fields were not completed: %s", "STARTS_KEY": "starts", "ENDS_KEY": "ends", "LOCATION_KEY": "location", "TITLE_KEY": "title", "DESCRIPTION_KEY": "description", "YOU_CAN_T_DO_THIS_WHILE_DEAD": "You can't do this while dead.", "YOU_CAN_T_DO_THIS_WHILE_RAGDOOLED": "You can't do this while ragdolled.", "YOU_ARE_ALREADY_DOING_AN_ACTION": "You are already doing an action.", "ATC_AIRCRAFT_HAIL_REQUEST": "Aircraft %s is hailing air traffic control", "ATC_AIRCRAFT_RADAR_REQUEST": "Aircraft %s has been detected", "welcome": {"actions": {"close": "PRESS {key}ESC{/key} TO CLOSE", "nextPage": "PRESS {key}E{/key} TO GO TO THE NEXT PAGE"}, "screen": {"pageIndicator": "{currentPage}/{totalPages}"}}, "interaction": {"vehicle": {"hood": "<PERSON>", "engine": "Engine", "lights": "Lights", "seat": "Seat {number}", "door": "Door {number}", "trunk": "Trunk"}}, "sceneMenu": {"submenus": {"text": "Text", "background": "Background", "other": "Other"}, "buttons": {"editPosition": "Edit Position", "save": "Save"}, "error": "An error occurred: {error}"}, "sceneEditor": {"text": {"sceneContent": "Scene Content", "font": "Font", "fontSize": "<PERSON><PERSON> Size (Max {max})", "fontColor": "Font Color", "fontOutline": "Font Outline", "fontOutlineColor": "Font Outline Color", "fontStyle": "Font Style", "placeholder": {"sceneContent": "Enter scene content...", "fontSize": "Enter font size..."}}, "other": {"rotation": "Rotation", "displayDistance": "Display Distance", "timeVisible": "Time Visible", "visibility": "Visibility", "closeDistance": "Close Distance"}, "background": {"background": "Background", "unknown": "Unknown", "change": "Change", "backgroundFill": "Background Fill", "backgroundColor": "Background Color", "backgroundOffsetX": "Background Offset X", "backgroundOffsetY": "Background Offset Y", "backgroundSizeX": "Background Size X", "backgroundSizeY": "Background Size Y"}}, "phoneBooth": {"dial": "<PERSON><PERSON>", "contactList": "Contact List", "delete": "Delete", "call": "Call", "endCall": "End Call", "duration": "Duration", "callStatus": {"dialing": "Dialing...", "ended": "Call Ended"}, "callEnded": "Call Ended"}, "ox": {"submit": "Submit", "cancel": "Cancel", "confirm": "Confirm"}, "jobBoard": {"task": {"active": "Active", "pay": "Pay", "time": "Time", "location": "Location", "cancel": "Cancel", "error": "You can only have one active task.", "timeLeft": "Time Left", "dailyReward": "Daily Reward"}}, "injuries": {"hold": "Hold", "inspect": {"title": {"self": "Your Injuries", "patient": "Inspected Patient Injuries"}, "actions": {"close": {"press": "Press", "key": "ESC", "description": "To Close"}, "camera": {"press": "Press", "key": "C", "description": "To change camera"}}}, "healing": {"pleaseWait": "Please wait...", "holdToGetOut": "to get out of bed"}, "knockedDown": {"title": "Your character is knocked down", "reviveText": "Please wait for players to help you"}, "unconscious": {"title": "Your character is unconscious", "respawn": "to respawn your character at the hospital", "waitToRespawn": "Wait until the timer runs out to respawn", "callEMS": "to call EMS", "alarmEMS": "You can alarm EMS in", "seconds": "seconds"}}, "hud": {"car": {"fuel": "FUEL", "engine": "ENG", "belt": "BELT", "flow": "FLOW"}}, "drugSelling": {"actionMessage": "Press {key}E{/key} to confirm the sale.", "sliderValue": "${value}"}, "dispatch": {"reactingUnits": "Reacting Units", "markOnMap": "Mark On Map", "react": "React", "leave": "Leave", "accept": "Accept"}, "crazyTaxi": {"timeLeft": "Time Left", "cashBonus": "Cash Bonus", "cashBonusValue": "${value}"}, "clothingMenu": {"hideClothes": "<PERSON><PERSON>", "saveEverything": "Save Everything", "exitWithoutSaving": "Are you sure you want to exit without saving?", "categories": "CATEGORIES", "available": "AVAILABLE", "undress": "UNDRESS", "variants": "VARIANTS"}, "pedSelector": {"useFemalePeds": "Use Female Peds", "pedModel": "PED MODEL", "available": "AVAILABLE"}, "faceFeatures": {"nose": "Nose", "eyebrows": "Eyebrows", "cheekBones": "Cheek Bones", "jawAndChin": "Jaw and Chin", "other": "Other", "eyeColor": "EYE COLOR"}, "faceMixSlider": {"faceMixing": "Face Mixing", "thirdFaceMix": "Third Face Mix", "shape": "<PERSON><PERSON><PERSON>", "color": "Color", "both": "Both"}, "faceSelector": {"available": "AVAILABLE", "shape": "<PERSON><PERSON><PERSON>", "color": "Color", "face": "Face", "faceIndex": "Face {index}"}, "chat": {"placeholder": "Enter your message or command...", "available": "AVAILABLE", "staff": "STAFF"}, "govCardMessage": {"losSantos": "LOS SANTOS", "governmentIdentifier": "GOVERNMENT IDENTIFIER", "name": "NAME", "stateId": "STATE ID", "dob": "DOB"}, "characterSelect": {"deleteConfirmation": "Are you sure you want to delete {first} {last}?", "deleteWarning": "This action cannot be undone!", "unexpectedError": "Unexpected error occurred! Relog and try again", "anyButton": "Press any button to continue...", "characterCard": {"createCharacter": "Create Character", "characterName": "Character Name", "lastPlayed": "Last Played"}}, "characterCreator": {"title": "Create your new character", "firstName": {"label": "First Name", "placeholder": "Enter your character's first name"}, "lastName": {"label": "Last Name", "placeholder": "Enter your character's last name"}, "pronouns": {"label": "Pronouns", "options": {"heHim": "He/Him", "sheHer": "She/Her", "theyThem": "They/Them", "other": "Other"}}, "gender": {"label": "Select Gender"}, "origin": {"label": "Select Country of Origin"}, "dob": {"label": "Select Date of Birth", "placeholder": "Select the Date"}, "buttons": {"back": "Back", "create": "Create your character"}, "errors": {"invalidName": "Invalid name!"}}, "cardealer": {"vehicleCarousel": {"selectVehicle": "Select a Vehicle", "searchPlaceholder": "Search for a vehicle", "allCategories": "All Categories", "allClasses": "All Classes", "showAll": "Show All", "showInStock": "Show In Stock"}, "vehicleColors": {"title": "COLOR", "randomTuning": "RANDOM VISUAL TUNING"}, "vehicleStats": {"class": "CLASS", "powerScore": "POWER SCORE", "price": "PRICE", "testDrive": "TEST DRIVE", "buy": "BUY", "stats": {"power": "POWER", "topSpeed": "TOP SPEED", "acceleration": "ACCELERATION", "braking": "BRAKES"}}, "vehicleThumbnail": {"inStock": "In Stock", "outOfStock": "Out of Stock", "class": "Class", "price": "Price", "limited": "LIMITED", "hot": "HOT", "currency": "${value}"}}, "animationMenu": {"selectAnimation": "Select Animation", "category": "Category", "searchPlaceholder": "Search animations", "noFound": "No matching animations found"}, "animationListItem": {"setBind": "<PERSON> Bind", "position": "Position", "bindLabel": {"ctrl": "CTRL", "shift": "SHIFT", "alt": "ALT", "numpad": "NUM"}}, "editKeybindModal": {"title": "Edit Your Keybinds", "waiting": "Waiting...", "description": "To use alphanumeric keys you need to hold shift/ctrl/alt too.\nYou can use numpad and function keys with or without shift/ctrl/alt.", "warning": "This keybind is already in use for /e {emote}!\nSaving will remove the bind for the conflicting emote!", "back": "Back", "clear": "Clear", "save": "Save", "error": "Unknown error occurred!"}, "overlays": {"chesthair": "Chest Hair", "bodyblemish": "Body Blemishes", "blush": "<PERSON><PERSON>", "ageing": "Ageing", "addbodyblemish": "Additional Blemishes", "freckles": "<PERSON><PERSON><PERSON>", "complexion": "Complexion", "lipstick": "Lipstick", "makeup": "Makeup", "blemish": "Face Blemishes", "facialhair": "Facial Hair", "eyebrows": "Eyebrows", "sundamage": "Sun Damage"}, "faceFeaturesCategory": {"width": "<PERSON><PERSON><PERSON>", "peakHeight": "Peak Height", "peakLength": "Peak Length", "boneHeight": "Bone Height", "peakLowering": "Peak Lowering", "boneTwist": "<PERSON> Twist", "height": "Height", "length": "Length", "boneHeight2": "Bone Height", "boneWidth": "<PERSON>", "width2": "<PERSON><PERSON><PERSON>", "eyeOpening": "Eye Opening", "lipThickness": "<PERSON><PERSON>", "jawWidth": "<PERSON><PERSON> W<PERSON>th", "jawLength": "Jaw Length", "chinHeight": "Chin Height", "chinLength": "Chin Length", "chinWidth": "<PERSON>", "chinDimple": "<PERSON>", "neckThickness": "Neck Thickness"}, "components": {"bracelet": "Bracelets", "hat": "Hats", "ear": "Earrings", "glass": "Glasses", "watch": "Watches", "face": "Ped Face", "badge": "Badges", "shirt": "Shirt", "bag": "Bags", "vests": "Vests", "hair": "Hair", "undershirt": "Undershirt", "pants": "<PERSON>ts", "accessories": "Accessories", "shoes": "Shoes", "arms": "Arms", "masks": "Masks", "chestHair": "Chest Hair", "eyebrows": "Eyebrows", "facialHair": "Facial Hair"}, "clothingTitles": {"shop": "CLOTHING SHOP", "creator": "CHARACTER CREATOR", "hair": "HAIR MENU"}, "categoryItems": {"face": {"face": "Face", "features": "Face Features", "overlays": "Face Overlays", "makeup": "Makeup"}, "hair": {"hair": "Hair", "beard": "<PERSON>", "eyebrows": "Eyebrows", "chesthair": "Chest Hair"}, "body": {"blemishes": "Body Blemishes"}, "ped": {"ped": "<PERSON>ed Selector"}, "tattoo": {"torsoTattoos": "<PERSON><PERSON>", "headTattoos": "Head Tattoos", "leftArmTattoos": "Left Arm Tattoos", "rightArmTattoos": "Right Arm Tattoos", "leftLegTattoos": "Left Leg Tattoos", "rightLegTattoos": "Right Leg <PERSON>"}, "runwayProps": {"runwayProps": "Runway Props"}}, "categories": {"face": "Face", "hair": "Hair", "clothes": "<PERSON><PERSON><PERSON>", "body": "Body", "tattoos": "Tattoos", "ped": "Ped", "runwayProps": "Runway Props"}, "eyeColors": {"green": "Green", "emerald": "Emerald", "lightBlue": "Light Blue", "oceanBlue": "Ocean Blue", "lightBrown": "<PERSON> Brown", "darkBrown": "<PERSON>", "hazel": "<PERSON>", "darkGray": "Dark Gray", "lightGray": "Light Gray", "pink": "Pink", "yellow": "Yellow", "purple": "Purple", "blackout": "Blackout", "shadesOfGray": "Shades of Gray", "tequilaSunrise": "Tequila Sunrise", "atomic": "Atomic", "warp": "Warp", "eCola": "ECola", "spaceRanger": "Space Ranger", "yingYang": "<PERSON>", "bullseye": "Bullseye", "lizard": "Lizard", "dragon": "Dragon", "extraTerrestrial": "Extra Terrestrial", "goat": "Goa<PERSON>", "smiley": "<PERSON><PERSON>", "possessed": "Possessed", "demon": "Demon", "infected": "Infected", "alien": "Alien", "undead": "Undead", "zombie": "Zombie"}, "overlayColor": {"primaryColor": "Primary Color", "secondaryColor": "Secondary Color"}, "cancel": "Cancel", "yes": "Yes", "currency": {"symbol": "USD"}}