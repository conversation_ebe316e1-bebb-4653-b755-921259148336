-- ███████╗██╗   ██╗███████╗███╗   ██╗████████╗███████╗
-- ██╔════╝██║   ██║██╔════╝████╗  ██║╚══██╔══╝██╔════╝
-- █████╗  ██║   ██║█████╗  ██╔██╗ ██║   ██║   ███████╗
-- ██╔══╝  ╚██╗ ██╔╝██╔══╝  ██║╚██╗██║   ██║   ╚════██║
-- ███████╗ ╚████╔╝ ███████╗██║ ╚████║   ██║   ███████║
-- ╚══════╝  ╚═══╝  ╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚══════╝
-- 
-- Valic HUD - Events Module
-- Author: Valic
-- Description: Event handling and management system

local QBCore = exports['qb-core']:GetCoreObject()

-- Event handlers registry
local eventHandlers = {}
local eventQueue = {}
local eventProcessing = false

-- Initialize events system
CreateThread(function()
    while true do
        if #eventQueue > 0 and not eventProcessing then
            ProcessEventQueue()
        end
        Wait(10)
    end
end)

-- Process event queue
function ProcessEventQueue()
    eventProcessing = true
    
    while #eventQueue > 0 do
        local event = table.remove(eventQueue, 1)
        if eventHandlers[event.name] then
            for _, handler in pairs(eventHandlers[event.name]) do
                handler(table.unpack(event.args))
            end
        end
        Wait(0)
    end
    
    eventProcessing = false
end

-- Register event handler
function RegisterEventHandler(eventName, handler)
    if not eventHandlers[eventName] then
        eventHandlers[eventName] = {}
    end
    table.insert(eventHandlers[eventName], handler)
end

-- Unregister event handler
function UnregisterEventHandler(eventName, handler)
    if eventHandlers[eventName] then
        for i, h in ipairs(eventHandlers[eventName]) do
            if h == handler then
                table.remove(eventHandlers[eventName], i)
                break
            end
        end
    end
end

-- Queue event for processing
function QueueEvent(eventName, ...)
    table.insert(eventQueue, {
        name = eventName,
        args = {...}
    })
end

-- Core game events
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    QueueEvent('valic_hud:playerLoaded')
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    QueueEvent('valic_hud:playerUnloaded')
end)

RegisterNetEvent('QBCore:Player:SetPlayerData', function(val)
    QueueEvent('valic_hud:playerDataUpdated', val)
end)

-- Vehicle events
local lastVehicle = nil
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            local playerPed = PlayerPedId()
            local currentVehicle = GetVehiclePedIsIn(playerPed, false)
            
            if currentVehicle ~= lastVehicle then
                if currentVehicle ~= 0 then
                    QueueEvent('valic_hud:enteredVehicle', currentVehicle)
                elseif lastVehicle ~= 0 then
                    QueueEvent('valic_hud:exitedVehicle', lastVehicle)
                end
                lastVehicle = currentVehicle
            end
        end
        Wait(500)
    end
end)

-- Health events
local lastHealth = 200
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            local currentHealth = GetEntityHealth(PlayerPedId())
            if currentHealth ~= lastHealth then
                QueueEvent('valic_hud:healthChanged', currentHealth, lastHealth)
                lastHealth = currentHealth
            end
        end
        Wait(1000)
    end
end)

-- Armor events
local lastArmor = 0
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            local currentArmor = GetPedArmour(PlayerPedId())
            if currentArmor ~= lastArmor then
                QueueEvent('valic_hud:armorChanged', currentArmor, lastArmor)
                lastArmor = currentArmor
            end
        end
        Wait(1000)
    end
end)

-- Weapon events
local lastWeapon = nil
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            local currentWeapon = GetSelectedPedWeapon(PlayerPedId())
            if currentWeapon ~= lastWeapon then
                QueueEvent('valic_hud:weaponChanged', currentWeapon, lastWeapon)
                lastWeapon = currentWeapon
            end
        end
        Wait(500)
    end
end)

-- Zone events
local lastZone = nil
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local currentZone = GetNameOfZone(playerCoords.x, playerCoords.y, playerCoords.z)
            if currentZone ~= lastZone then
                QueueEvent('valic_hud:zoneChanged', currentZone, lastZone)
                lastZone = currentZone
            end
        end
        Wait(2000)
    end
end)

-- Job events
RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    QueueEvent('valic_hud:jobChanged', JobInfo)
end)

RegisterNetEvent('QBCore:Client:OnGangUpdate', function(GangInfo)
    QueueEvent('valic_hud:gangChanged', GangInfo)
end)

-- Money events
RegisterNetEvent('hud:client:OnMoneyChange', function(type, amount, reason)
    QueueEvent('valic_hud:moneyChanged', type, amount, reason)
end)

-- Status events
RegisterNetEvent('hud:client:UpdateNeeds', function(newHunger, newThirst)
    QueueEvent('valic_hud:needsChanged', newHunger, newThirst)
end)

RegisterNetEvent('hud:client:UpdateStress', function(newStress)
    QueueEvent('valic_hud:stressChanged', newStress)
end)

-- Voice events
RegisterNetEvent('pma-voice:setTalkingMode', function(mode)
    QueueEvent('valic_hud:voiceModeChanged', mode)
end)

RegisterNetEvent('pma-voice:radioActive', function(active)
    QueueEvent('valic_hud:radioActiveChanged', active)
end)

-- Weather events
local lastWeather = nil
CreateThread(function()
    while true do
        local currentWeather = GetPrevWeatherTypeHashName()
        if currentWeather ~= lastWeather then
            QueueEvent('valic_hud:weatherChanged', currentWeather, lastWeather)
            lastWeather = currentWeather
        end
        Wait(30000) -- Check every 30 seconds
    end
end)

-- Time events
local lastHour = -1
CreateThread(function()
    while true do
        local currentHour = GetClockHours()
        if currentHour ~= lastHour then
            QueueEvent('valic_hud:hourChanged', currentHour, lastHour)
            lastHour = currentHour
        end
        Wait(60000) -- Check every minute
    end
end)

-- Custom events for other resources
RegisterNetEvent('valic_hud:client:triggerEvent', function(eventName, ...)
    QueueEvent(eventName, ...)
end)

-- Event handlers for HUD updates
RegisterEventHandler('valic_hud:playerLoaded', function()
    TriggerEvent('valic_hud:client:initializeHUD')
end)

RegisterEventHandler('valic_hud:enteredVehicle', function(vehicle)
    TriggerEvent('valic_hud:client:enteredVehicle', vehicle)
end)

RegisterEventHandler('valic_hud:exitedVehicle', function(vehicle)
    TriggerEvent('valic_hud:client:exitedVehicle', vehicle)
end)

RegisterEventHandler('valic_hud:healthChanged', function(newHealth, oldHealth)
    if newHealth <= 100 and oldHealth > 100 then
        -- Player died
        QueueEvent('valic_hud:playerDied')
    elseif newHealth > 100 and oldHealth <= 100 then
        -- Player revived
        QueueEvent('valic_hud:playerRevived')
    end
end)

-- Exports
exports('registerEventHandler', RegisterEventHandler)
exports('unregisterEventHandler', UnregisterEventHandler)
exports('queueEvent', QueueEvent)
exports('getEventHandlers', function()
    return eventHandlers
end)
exports('getEventQueue', function()
    return eventQueue
end)
