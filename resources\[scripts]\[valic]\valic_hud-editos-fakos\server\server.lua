-- ██╗   ██╗ █████╗ ██╗     ██╗ ██████╗    ██╗  ██╗██╗   ██╗██████╗ 
-- ██║   ██║██╔══██╗██║     ██║██╔════╝    ██║  ██║██║   ██║██╔══██╗
-- ██║   ██║███████║██║     ██║██║         ███████║██║   ██║██║  ██║
-- ╚██╗ ██╔╝██╔══██║██║     ██║██║         ██╔══██║██║   ██║██║  ██║
--  ╚████╔╝ ██║  ██║███████╗██║╚██████╗    ██║  ██║╚██████╔╝██████╔╝
--   ╚═══╝  ╚═╝  ╚═╝╚══════╝╚═╝ ╚═════╝    ╚═╝  ╚═╝ ╚═════╝ ╚═════╝ 
-- 
-- Valic HUD - Main Server Script
-- Author: Valic
-- Description: Server-side functionality and data management

local QBCore = exports['qb-core']:GetCoreObject()

-- Player data storage
local playerSettings = {}
local playerData = {}

-- Initialize server
CreateThread(function()
    print("^2[Valic HUD]^7 Server initialized successfully!")
end)

-- Player connecting
RegisterNetEvent('QBCore:Server:PlayerLoaded', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    -- Initialize player data
    playerData[src] = {
        citizenid = Player.PlayerData.citizenid,
        name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
        job = Player.PlayerData.job,
        gang = Player.PlayerData.gang,
        metadata = Player.PlayerData.metadata
    }
    
    -- Load player settings
    LoadPlayerSettings(src, Player.PlayerData.citizenid)
end)

-- Player disconnecting
AddEventHandler('playerDropped', function()
    local src = source
    if playerData[src] then
        playerData[src] = nil
    end
    if playerSettings[src] then
        playerSettings[src] = nil
    end
end)

-- Load player settings
function LoadPlayerSettings(src, citizenid)
    local result = MySQL.query.await('SELECT settings FROM valic_hud_settings WHERE citizenid = ?', {citizenid})
    
    if result and result[1] then
        local settings = json.decode(result[1].settings)
        playerSettings[src] = settings
        TriggerClientEvent('valic_hud:client:updateSettings', src, settings)
    else
        -- Create default settings
        local defaultSettings = Presets.DEFAULT.Settings
        playerSettings[src] = defaultSettings
        
        MySQL.insert('INSERT INTO valic_hud_settings (citizenid, settings) VALUES (?, ?)', {
            citizenid,
            json.encode(defaultSettings)
        })
        
        TriggerClientEvent('valic_hud:client:updateSettings', src, defaultSettings)
    end
end

-- Save player settings
RegisterNetEvent('valic_hud:server:saveSettings', function(settings)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    playerSettings[src] = settings
    
    MySQL.update('UPDATE valic_hud_settings SET settings = ? WHERE citizenid = ?', {
        json.encode(settings),
        Player.PlayerData.citizenid
    })
end)

-- Save HUD settings
RegisterNetEvent('valic_hud:server:saveHudSettings', function(settings)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    if not playerSettings[src] then
        playerSettings[src] = {}
    end
    
    playerSettings[src].hud = settings
    
    MySQL.update('UPDATE valic_hud_settings SET settings = ? WHERE citizenid = ?', {
        json.encode(playerSettings[src]),
        Player.PlayerData.citizenid
    })
end)

-- Give cash to player
RegisterNetEvent('valic_hud:server:giveCash', function(targetId, amount)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local Target = QBCore.Functions.GetPlayer(targetId)
    
    if not Player or not Target then return end
    if amount <= 0 then return end
    
    if Player.PlayerData.money.cash >= amount then
        Player.Functions.RemoveMoney('cash', amount, 'gave-cash')
        Target.Functions.AddMoney('cash', amount, 'received-cash')
        
        TriggerClientEvent('QBCore:Notify', src, 'You gave $' .. amount .. ' to ' .. Target.PlayerData.charinfo.firstname, 'success')
        TriggerClientEvent('QBCore:Notify', targetId, 'You received $' .. amount .. ' from ' .. Player.PlayerData.charinfo.firstname, 'success')
    else
        TriggerClientEvent('QBCore:Notify', src, 'You don\'t have enough cash', 'error')
    end
end)

-- Show ID to player
RegisterNetEvent('valic_hud:server:showId', function(targetId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local Target = QBCore.Functions.GetPlayer(targetId)
    
    if not Player or not Target then return end
    
    local idData = {
        name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
        dob = Player.PlayerData.charinfo.birthdate,
        gender = Player.PlayerData.charinfo.gender == 0 and 'Male' or 'Female',
        nationality = Player.PlayerData.charinfo.nationality,
        citizenid = Player.PlayerData.citizenid
    }
    
    TriggerClientEvent('valic_hud:client:showIdCard', targetId, idData)
end)

-- Get player data
QBCore.Functions.CreateCallback('valic_hud:server:getPlayerData', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then 
        cb(nil)
        return 
    end
    
    cb({
        citizenid = Player.PlayerData.citizenid,
        name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
        job = Player.PlayerData.job,
        gang = Player.PlayerData.gang,
        money = Player.PlayerData.money,
        metadata = Player.PlayerData.metadata
    })
end)

-- Get player settings
QBCore.Functions.CreateCallback('valic_hud:server:getSettings', function(source, cb)
    cb(playerSettings[source] or {})
end)

-- Update player metadata
RegisterNetEvent('valic_hud:server:updateMetadata', function(key, value)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    Player.Functions.SetMetaData(key, value)
    
    if playerData[src] then
        playerData[src].metadata[key] = value
    end
end)

-- Stress system
RegisterNetEvent('valic_hud:server:updateStress', function(amount)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local currentStress = Player.PlayerData.metadata.stress or 0
    local newStress = math.max(0, math.min(100, currentStress + amount))
    
    Player.Functions.SetMetaData('stress', newStress)
    TriggerClientEvent('hud:client:UpdateStress', src, newStress)
end)

-- Admin commands
QBCore.Commands.Add('sethudpreset', 'Set HUD preset for player', {{name = 'id', help = 'Player ID'}, {name = 'preset', help = 'Preset name (DEFAULT/LEGACY)'}}, true, function(source, args)
    local targetId = tonumber(args[1])
    local presetName = string.upper(args[2])
    
    if not targetId or not presetName then
        TriggerClientEvent('QBCore:Notify', source, 'Invalid arguments', 'error')
        return
    end
    
    local Target = QBCore.Functions.GetPlayer(targetId)
    if not Target then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    if not Presets[presetName] then
        TriggerClientEvent('QBCore:Notify', source, 'Preset not found', 'error')
        return
    end
    
    local settings = Presets[presetName].Settings
    playerSettings[targetId] = { hud = settings }
    
    MySQL.update('UPDATE valic_hud_settings SET settings = ? WHERE citizenid = ?', {
        json.encode(playerSettings[targetId]),
        Target.PlayerData.citizenid
    })
    
    TriggerClientEvent('valic_hud:client:updateHudSettings', targetId, settings)
    TriggerClientEvent('QBCore:Notify', source, 'HUD preset set for player', 'success')
    TriggerClientEvent('QBCore:Notify', targetId, 'Your HUD preset has been updated', 'info')
end, 'admin')

-- Database initialization
CreateThread(function()
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `valic_hud_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `citizenid` varchar(50) NOT NULL,
            `settings` longtext NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `citizenid` (`citizenid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
end)

-- Exports
exports('getPlayerSettings', function(src)
    return playerSettings[src]
end)

exports('setPlayerSettings', function(src, settings)
    playerSettings[src] = settings
end)

exports('getPlayerData', function(src)
    return playerData[src]
end)
