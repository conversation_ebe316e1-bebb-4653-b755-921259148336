-- Valic HUD - Payphone Module
local QBCore = exports['qb-core']:GetCoreObject()

RegisterNetEvent('valic_hud:client:openPayphone', function()
    SendNUIMessage({
        action = 'showPayphone'
    })
    SetNuiFocus(true, true)
end)

RegisterNUICallback('payphoneCall', function(data, cb)
    -- Handle payphone call
    TriggerServerEvent('valic_hud:server:payphoneCall', data.number)
    cb('ok')
end)

RegisterNUICallback('closePayphone', function(data, cb)
    SetNuiFocus(false, false)
    cb('ok')
end)