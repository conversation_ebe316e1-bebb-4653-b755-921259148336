-- ██████╗ ██████╗  ██████╗  ██████╗ ██████╗ ███████╗███████╗███████╗
-- ██╔══██╗██╔══██╗██╔═══██╗██╔════╝ ██╔══██╗██╔════╝██╔════╝██╔════╝
-- ██████╔╝██████╔╝██║   ██║██║  ███╗██████╔╝█████╗  ███████╗███████╗
-- ██╔═══╝ ██╔══██╗██║   ██║██║   ██║██╔══██╗██╔══╝  ╚════██║╚════██║
-- ██║     ██║  ██║╚██████╔╝╚██████╔╝██║  ██║███████╗███████║███████║
-- ╚═╝     ╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝
-- 
-- Valic HUD - Progress Bar Module
-- Author: Valic
-- Description: Advanced progress bar system with animations and props

local QBCore = exports['qb-core']:GetCoreObject()

-- Progress state
local progressActive = false
local progressData = {}
local progressStartTime = 0
local progressAnimation = {}
local progressProps = {}

-- Initialize progress system
RegisterNetEvent('valic_hud:client:setupProgress', function()
    -- Setup is handled automatically
end)

-- Show progress bar
function ShowProgress(data)
    if progressActive then
        return false
    end
    
    progressActive = true
    progressData = data
    progressStartTime = GetGameTimer()
    
    -- Validate required data
    if not data.label or not data.duration then
        progressActive = false
        return false
    end
    
    -- Set defaults
    data.useWhileDead = data.useWhileDead or false
    data.canCancel = data.canCancel or false
    data.disableControls = data.disableControls or {}
    
    -- Handle animation
    if data.animation then
        PlayProgressAnimation(data.animation)
    end
    
    -- Handle props
    if data.prop then
        CreateProgressProp(data.prop)
    end
    
    if data.propTwo then
        CreateProgressProp(data.propTwo, true)
    end
    
    -- Send to UI
    SendNUIMessage({
        action = 'showProgress',
        data = {
            label = data.label,
            duration = data.duration,
            canCancel = data.canCancel,
            icon = data.icon,
            color = data.color
        }
    })
    
    -- Start progress thread
    CreateThread(function()
        local startTime = GetGameTimer()
        
        while progressActive do
            local currentTime = GetGameTimer()
            local elapsed = currentTime - startTime
            local progress = math.min(elapsed / data.duration, 1.0)
            
            -- Update progress
            SendNUIMessage({
                action = 'updateProgress',
                data = {
                    progress = progress * 100
                }
            })
            
            -- Check for completion
            if elapsed >= data.duration then
                FinishProgress(true)
                break
            end
            
            -- Check for cancellation
            if data.canCancel and IsControlJustPressed(0, 73) then -- X key
                FinishProgress(false)
                break
            end
            
            -- Check if player is dead and shouldn't continue
            if not data.useWhileDead and IsEntityDead(PlayerPedId()) then
                FinishProgress(false)
                break
            end
            
            -- Disable controls
            if data.disableControls then
                DisableProgressControls(data.disableControls)
            end
            
            Wait(10)
        end
    end)
    
    return true
end

-- Finish progress
function FinishProgress(success)
    if not progressActive then return end
    
    progressActive = false
    
    -- Clean up animation
    if progressAnimation.dict then
        StopAnimTask(PlayerPedId(), progressAnimation.dict, progressAnimation.anim, 1.0)
        progressAnimation = {}
    end
    
    -- Clean up props
    for _, prop in pairs(progressProps) do
        if DoesEntityExist(prop.entity) then
            DeleteEntity(prop.entity)
        end
    end
    progressProps = {}
    
    -- Hide UI
    SendNUIMessage({
        action = 'hideProgress'
    })
    
    -- Trigger callback
    if progressData.onFinish then
        progressData.onFinish(success)
    end
    
    -- Trigger event
    TriggerEvent('valic_hud:client:progressFinished', success, progressData)
    
    progressData = {}
end

-- Play animation
function PlayProgressAnimation(animData)
    local playerPed = PlayerPedId()
    
    if animData.dict and animData.anim then
        RequestAnimDict(animData.dict)
        while not HasAnimDictLoaded(animData.dict) do
            Wait(10)
        end
        
        local flags = animData.flags or 49
        local blendIn = animData.blendIn or 8.0
        local blendOut = animData.blendOut or 8.0
        local duration = animData.duration or -1
        local playbackRate = animData.playbackRate or 0
        local lockX = animData.lockX or false
        local lockY = animData.lockY or false
        local lockZ = animData.lockZ or false
        
        TaskPlayAnim(playerPed, animData.dict, animData.anim, blendIn, blendOut, duration, flags, playbackRate, lockX, lockY, lockZ)
        
        progressAnimation = {
            dict = animData.dict,
            anim = animData.anim
        }
    elseif animData.scenario then
        TaskStartScenarioInPlace(playerPed, animData.scenario, 0, true)
        progressAnimation = {
            scenario = animData.scenario
        }
    end
end

-- Create prop
function CreateProgressProp(propData, isSecondProp)
    local playerPed = PlayerPedId()
    local propHash = GetHashKey(propData.model)
    
    RequestModel(propHash)
    while not HasModelLoaded(propHash) do
        Wait(10)
    end
    
    local prop = CreateObject(propHash, 0.0, 0.0, 0.0, true, true, false)
    local bone = GetPedBoneIndex(playerPed, propData.bone or 60309)
    
    AttachEntityToEntity(
        prop, playerPed, bone,
        propData.pos.x or 0.0, propData.pos.y or 0.0, propData.pos.z or 0.0,
        propData.rot.x or 0.0, propData.rot.y or 0.0, propData.rot.z or 0.0,
        true, true, false, true, 1, true
    )
    
    SetModelAsNoLongerNeeded(propHash)
    
    table.insert(progressProps, {
        entity = prop,
        model = propData.model,
        isSecond = isSecondProp or false
    })
end

-- Disable controls during progress
function DisableProgressControls(controls)
    for _, control in pairs(controls) do
        DisableControlAction(0, control, true)
    end
end

-- Events
RegisterNetEvent('valic_hud:client:progress', function(data, cb)
    local success = ShowProgress(data)
    if cb then cb(success) end
end)

RegisterNetEvent('valic_hud:client:progressCancel', function()
    if progressActive then
        FinishProgress(false)
    end
end)

-- NUI Callbacks
RegisterNUICallback('progressCancel', function(data, cb)
    if progressActive and progressData.canCancel then
        FinishProgress(false)
    end
    cb('ok')
end)

-- QBCore progressbar override
if GetResourceState('progressbar') == 'started' then
    exports['progressbar']:Progress = function(data, cb)
        local progressData = {
            label = data.label,
            duration = data.duration,
            useWhileDead = data.useWhileDead,
            canCancel = data.canCancel,
            disableControls = data.disableControls,
            animation = data.animation,
            prop = data.prop,
            propTwo = data.propTwo,
            onFinish = cb
        }
        
        ShowProgress(progressData)
    end
end

-- Skill check integration
RegisterNetEvent('valic_hud:client:skillCheck', function(data, cb)
    SendNUIMessage({
        action = 'showSkillCheck',
        data = {
            difficulty = data.difficulty or 'medium',
            keys = data.keys or {'q', 'w', 'e', 'r'},
            duration = data.duration or 5000
        }
    })
    SetNuiFocus(false, true)
    
    -- Store callback for later use
    skillCheckCallback = cb
end)

RegisterNUICallback('skillCheckResult', function(data, cb)
    SetNuiFocus(false, false)
    if skillCheckCallback then
        skillCheckCallback(data.success)
        skillCheckCallback = nil
    end
    cb('ok')
end)

-- Exports
exports('showProgress', ShowProgress)
exports('hideProgress', function()
    if progressActive then
        FinishProgress(false)
    end
end)
exports('isProgressActive', function()
    return progressActive
end)
exports('getProgressData', function()
    return progressData
end)
