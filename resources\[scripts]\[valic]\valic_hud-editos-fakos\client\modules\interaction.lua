-- ██╗███╗   ██╗████████╗███████╗██████╗  █████╗  ██████╗████████╗██╗ ██████╗ ███╗   ██╗
-- ██║████╗  ██║╚══██╔══╝██╔════╝██╔══██╗██╔══██╗██╔════╝╚══██╔══╝██║██╔═══██╗████╗  ██║
-- ██║██╔██╗ ██║   ██║   █████╗  ██████╔╝███████║██║        ██║   ██║██║   ██║██╔██╗ ██║
-- ██║██║╚██╗██║   ██║   ██╔══╝  ██╔══██╗██╔══██║██║        ██║   ██║██║   ██║██║╚██╗██║
-- ██║██║ ╚████║   ██║   ███████╗██║  ██║██║  ██║╚██████╗   ██║   ██║╚██████╔╝██║ ╚████║
-- ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝
-- 
-- Valic HUD - Interaction Module
-- Author: Valic
-- Description: Advanced interaction menu system

local QBCore = exports['qb-core']:GetCoreObject()

-- Interaction state
local interactionMenuOpen = false
local currentInteractions = {}
local nearbyInteractions = {}

-- Initialize interactions
RegisterNetEvent('valic_hud:client:setupInteractions', function()
    CreateThread(function()
        while true do
            if LocalPlayer.state.isLoggedIn then
                CheckNearbyInteractions()
            end
            Wait(500)
        end
    end)
end)

-- Check for nearby interactions
function CheckNearbyInteractions()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    
    nearbyInteractions = {}
    
    -- Vehicle interactions
    if vehicle ~= 0 then
        AddVehicleInteractions(vehicle)
    else
        -- On foot interactions
        AddPlayerInteractions(playerCoords)
        AddWorldInteractions(playerCoords)
    end
    
    -- Update UI
    SendNUIMessage({
        action = 'updateInteractions',
        data = nearbyInteractions
    })
end

-- Add vehicle interactions
function AddVehicleInteractions(vehicle)
    local isDriver = GetPedInVehicleSeat(vehicle, -1) == PlayerPedId()
    
    if isDriver then
        table.insert(nearbyInteractions, {
            id = 'toggle_engine',
            label = locale('TOGGLE_ENGINE'),
            icon = 'fas fa-power-off',
            action = 'valic_hud:client:toggleEngine'
        })
        
        table.insert(nearbyInteractions, {
            id = 'give_keys',
            label = locale('GIVE_KEYS_INTERACTION_LABEL'),
            icon = 'fas fa-key',
            action = 'valic_hud:client:giveKeys'
        })
    end
    
    table.insert(nearbyInteractions, {
        id = 'toggle_seatbelt',
        label = 'Toggle Seatbelt',
        icon = 'fas fa-user-shield',
        action = 'valic_hud:client:toggleSeatbelt'
    })
end

-- Add player interactions
function AddPlayerInteractions(coords)
    local closestPlayer, distance = QBCore.Functions.GetClosestPlayer(coords)
    
    if closestPlayer ~= -1 and distance <= 3.0 then
        table.insert(nearbyInteractions, {
            id = 'give_cash',
            label = 'Give Cash',
            icon = 'fas fa-dollar-sign',
            action = 'valic_hud:client:giveCash',
            data = { playerId = GetPlayerServerId(closestPlayer) }
        })
        
        table.insert(nearbyInteractions, {
            id = 'show_id',
            label = 'Show ID',
            icon = 'fas fa-id-card',
            action = 'valic_hud:client:showId',
            data = { playerId = GetPlayerServerId(closestPlayer) }
        })
    end
end

-- Add world interactions
function AddWorldInteractions(coords)
    -- ATM interactions
    local atm = GetClosestObjectOfType(coords, 3.0, GetHashKey('prop_atm_01'), false, false, false)
    if atm ~= 0 then
        table.insert(nearbyInteractions, {
            id = 'use_atm',
            label = 'Use ATM',
            icon = 'fas fa-credit-card',
            action = 'valic_hud:client:useATM'
        })
    end
    
    -- Payphone interactions
    local payphone = GetClosestObjectOfType(coords, 2.0, GetHashKey('prop_phonebox_01a'), false, false, false)
    if payphone ~= 0 then
        table.insert(nearbyInteractions, {
            id = 'use_payphone',
            label = locale('USE_PAYPHONE_LABEL'),
            icon = 'fas fa-phone',
            action = 'valic_hud:client:usePayphone'
        })
    end
end

-- Show interaction menu
function ShowInteractionMenu()
    if #nearbyInteractions == 0 then
        QBCore.Functions.Notify('No interactions available', 'error')
        return
    end
    
    interactionMenuOpen = true
    
    SendNUIMessage({
        action = 'showInteractionMenu',
        data = {
            interactions = nearbyInteractions,
            title = 'Interactions'
        }
    })
    
    SetNuiFocus(true, true)
end

-- Hide interaction menu
function HideInteractionMenu()
    if not interactionMenuOpen then return end
    
    interactionMenuOpen = false
    
    SendNUIMessage({
        action = 'hideInteractionMenu'
    })
    
    SetNuiFocus(false, false)
end

-- Handle interaction selection
function HandleInteraction(interactionId, data)
    HideInteractionMenu()
    
    for _, interaction in pairs(nearbyInteractions) do
        if interaction.id == interactionId then
            TriggerEvent(interaction.action, interaction.data)
            break
        end
    end
end

-- Commands
RegisterCommand('showinteraction', function()
    ShowInteractionMenu()
end)

-- Events
RegisterNetEvent('valic_hud:client:toggleEngine', function()
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    if vehicle ~= 0 and GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() then
        local engineOn = GetIsVehicleEngineRunning(vehicle)
        SetVehicleEngineOn(vehicle, not engineOn, false, true)
    end
end)

RegisterNetEvent('valic_hud:client:toggleSeatbelt', function()
    TriggerEvent('seatbelt:client:ToggleSeatbelt')
end)

RegisterNetEvent('valic_hud:client:giveCash', function(data)
    local amount = exports['valic_hud-editos-fakos']:showNumberInput('Give Cash', 1, 10000, true)
    if amount then
        TriggerServerEvent('valic_hud:server:giveCash', data.playerId, amount)
    end
end)

RegisterNetEvent('valic_hud:client:showId', function(data)
    TriggerServerEvent('valic_hud:server:showId', data.playerId)
end)

RegisterNetEvent('valic_hud:client:useATM', function()
    TriggerEvent('qb-atms:client:openATM')
end)

RegisterNetEvent('valic_hud:client:usePayphone', function()
    TriggerEvent('valic_hud:client:openPayphone')
end)

-- NUI Callbacks
RegisterNUICallback('selectInteraction', function(data, cb)
    HandleInteraction(data.id, data.data)
    cb('ok')
end)

RegisterNUICallback('closeInteractionMenu', function(data, cb)
    HideInteractionMenu()
    cb('ok')
end)

-- Custom interaction system
local customInteractions = {}

function AddCustomInteraction(id, data)
    customInteractions[id] = data
end

function RemoveCustomInteraction(id)
    customInteractions[id] = nil
end

function GetCustomInteractions(coords)
    local interactions = {}
    
    for id, interaction in pairs(customInteractions) do
        if interaction.coords then
            local distance = #(coords - interaction.coords)
            if distance <= (interaction.distance or 3.0) then
                table.insert(interactions, {
                    id = id,
                    label = interaction.label,
                    icon = interaction.icon,
                    action = interaction.action,
                    data = interaction.data
                })
            end
        end
    end
    
    return interactions
end

-- Exports
exports('showInteractionMenu', ShowInteractionMenu)
exports('hideInteractionMenu', HideInteractionMenu)
exports('addCustomInteraction', AddCustomInteraction)
exports('removeCustomInteraction', RemoveCustomInteraction)
exports('isInteractionMenuOpen', function()
    return interactionMenuOpen
end)
exports('getNearbyInteractions', function()
    return nearbyInteractions
end)
