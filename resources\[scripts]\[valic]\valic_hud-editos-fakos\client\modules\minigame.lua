-- Valic HUD - Minigame Module
local QBCore = exports['qb-core']:GetCoreObject()

local minigameCallback = nil

RegisterNetEvent('valic_hud:client:startMinigame', function(gameType, difficulty, callback)
    minigameCallback = callback
    SendNUIMessage({
        action = 'showMinigame',
        data = {
            type = gameType,
            difficulty = difficulty
        }
    })
    SetNuiFocus(false, true)
end)

RegisterNUICallback('minigameResult', function(data, cb)
    SetNuiFocus(false, false)
    if minigameCallback then
        minigameCallback(data.success, data.score)
        minigameCallback = nil
    end
    cb('ok')
end)