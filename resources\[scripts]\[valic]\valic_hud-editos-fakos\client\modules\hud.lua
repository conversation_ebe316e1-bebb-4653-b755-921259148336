-- ██╗  ██╗██╗   ██╗██████╗ 
-- ██║  ██║██║   ██║██╔══██╗
-- ███████║██║   ██║██║  ██║
-- ██╔══██║██║   ██║██║  ██║
-- ██║  ██║╚██████╔╝██████╔╝
-- ╚═╝  ╚═╝ ╚═════╝ ╚═════╝ 
-- 
-- Valic HUD - Main HUD Module
-- Author: Valic
-- Description: Main HUD functionality and display

local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = QBCore.Functions.GetPlayerData()

-- HUD State
local hudActive = true
local showIds = false
local hudSettings = {}

-- Update intervals
local HUD_UPDATE_INTERVAL = 250
local LOCATION_UPDATE_INTERVAL = 1000

-- Initialize HUD
RegisterNetEvent('valic_hud:client:setupHUD', function()
    CreateThread(function()
        while true do
            if hudActive and LocalPlayer.state.isLoggedIn then
                UpdateHUD()
            end
            Wait(HUD_UPDATE_INTERVAL)
        end
    end)
    
    CreateThread(function()
        while true do
            if hudActive and LocalPlayer.state.isLoggedIn then
                UpdateLocation()
            end
            Wait(LOCATION_UPDATE_INTERVAL)
        end
    end)
end)

-- Update main HUD data
function UpdateHUD()
    local playerStats = ValicHUD.Player.GetPlayerStats()
    local weaponData = ValicHUD.Player.GetWeaponData()
    local timeData = ValicHUD.Utils.GetGameTime()
    
    if not playerStats then return end
    
    local hudData = {
        health = math.floor((playerStats.health - 100) / 100 * 100),
        armor = playerStats.armor,
        hunger = playerStats.hunger,
        thirst = playerStats.thirst,
        stress = playerStats.stress,
        money = {
            cash = playerStats.money.cash or 0,
            bank = playerStats.money.bank or 0
        },
        weapon = weaponData,
        time = timeData,
        weather = ValicHUD.Utils.GetWeather(),
        showIds = showIds
    }
    
    -- Check if weapon should show ammo
    if weaponData and HudConfig.ExcludedWeapons[weaponData.weapon] then
        hudData.weapon.hideAmmo = true
    end
    
    if weaponData and HudConfig.ForceHideAmmoWeapons[weaponData.weapon] then
        hudData.weapon.forceHideAmmo = true
    end
    
    SendNUIMessage({
        action = 'updateHUD',
        data = hudData
    })
end

-- Update location data
function UpdateLocation()
    local locationData = ValicHUD.Player.GetLocationData()
    
    SendNUIMessage({
        action = 'updateLocation',
        data = locationData
    })
end

-- Toggle HUD visibility
RegisterNetEvent('valic_hud:client:toggleHUD', function()
    hudActive = not hudActive
    
    SendNUIMessage({
        action = 'toggleHUD',
        visible = hudActive
    })
    
    QBCore.Functions.Notify(hudActive and locale('HUD_ENABLED') or locale('HUD_DISABLED'), 'primary')
end)

-- Show IDs
RegisterCommand('+showids', function()
    showIds = true
    SendNUIMessage({
        action = 'toggleIds',
        show = true
    })
end)

RegisterCommand('-showids', function()
    showIds = false
    SendNUIMessage({
        action = 'toggleIds',
        show = false
    })
end)

-- HUD Settings
RegisterNetEvent('valic_hud:client:updateHudSettings', function(settings)
    hudSettings = settings
    
    SendNUIMessage({
        action = 'updateHudSettings',
        settings = settings
    })
end)

RegisterNetEvent('valic_hud:client:openHudSettings', function()
    SendNUIMessage({
        action = 'openHudSettings',
        presets = Presets,
        currentSettings = hudSettings
    })
    SetNuiFocus(true, true)
end)

-- NUI Callbacks
RegisterNUICallback('saveHudSettings', function(data, cb)
    hudSettings = data.settings
    TriggerServerEvent('valic_hud:server:saveHudSettings', hudSettings)
    
    QBCore.Functions.Notify(locale('SAVED_HUD_SETTINGS'), 'success')
    cb('ok')
end)

RegisterNUICallback('loadPreset', function(data, cb)
    local preset = Presets[data.preset]
    if preset then
        hudSettings = preset.Settings
        TriggerServerEvent('valic_hud:server:saveHudSettings', hudSettings)
        
        SendNUIMessage({
            action = 'updateHudSettings',
            settings = hudSettings
        })
        
        QBCore.Functions.Notify('Preset loaded: ' .. preset.Label, 'success')
    end
    cb('ok')
end)

-- Player data updates
RegisterNetEvent('QBCore:Player:SetPlayerData', function(val)
    PlayerData = val
end)

RegisterNetEvent('hud:client:UpdateNeeds', function(newHunger, newThirst)
    -- Update needs from other resources
    SendNUIMessage({
        action = 'updateNeeds',
        data = {
            hunger = newHunger,
            thirst = newThirst
        }
    })
end)

RegisterNetEvent('hud:client:UpdateStress', function(newStress)
    -- Update stress from other resources
    SendNUIMessage({
        action = 'updateStress',
        data = {
            stress = newStress
        }
    })
end)

-- Money updates
RegisterNetEvent('hud:client:OnMoneyChange', function(type, amount, reason)
    local playerData = QBCore.Functions.GetPlayerData()
    if not playerData then return end
    
    SendNUIMessage({
        action = 'updateMoney',
        data = {
            cash = playerData.money.cash or 0,
            bank = playerData.money.bank or 0,
            change = {
                type = type,
                amount = amount,
                reason = reason
            }
        }
    })
end)

-- Voice indicator
RegisterNetEvent('pma-voice:setTalkingMode', function(mode)
    SendNUIMessage({
        action = 'updateVoice',
        data = {
            talking = false,
            mode = mode
        }
    })
end)

RegisterNetEvent('pma-voice:radioActive', function(active)
    SendNUIMessage({
        action = 'updateVoice',
        data = {
            radio = active
        }
    })
end)

-- Exports
exports('toggleHUD', function()
    TriggerEvent('valic_hud:client:toggleHUD')
end)

exports('updateHUD', function(data)
    SendNUIMessage({
        action = 'updateHUD',
        data = data
    })
end)

exports('getHudActive', function()
    return hudActive
end)
