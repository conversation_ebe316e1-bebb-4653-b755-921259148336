{"UNKNOWN": "Okä<PERSON>", "KEYBIND_SHOW_INTERACTION_MENU": "Hud - Visa Interaktionsmeny", "KEYBIND_TOGGLE_HUD": "Hud - Växla HUD", "KEYBIND_HUD_SHOW_IDS": "Hud - Visa ID:n (Håll)", "VEHICLE_ACTIONS_LABEL": "Fordonshandlingar", "GIVE_KEYS_INTERACTION_LABEL": "Ge Nycklar", "NO_ONE_TO_GIVE_KEYS": "Det finns ingen att ge nycklar till.", "GAVE_KEYS": "Du gav nycklar till fordonet.", "COULDNT_GIVE_KEYS": "Du kunde inte ge nycklar till fordonet.", "SAVED_HUD_SETTINGS": "Du sparade dina HUD-inställningar.", "GPS_ACTIVATED": "GPS aktiverad", "GPS_DISABLED": "GPS avstängd", "ALREADY_DOING_ACTION": "<PERSON><PERSON><PERSON><PERSON><PERSON> en <PERSON>", "USE_PAYPHONE_LABEL": "Använd betaltelefon", "CALL_ENDED": "Samtal avslutat", "FIELDS_NOT_COMPLETE": "Följande fält är inte ifyllda: %s", "INVALID_SID": "Ogiltigt statligt ID", "DATE_LC": "datum", "NAME_LC": "namn", "SID_LC": "statligt ID", "VIOLATION_LC": "överträdelser", "REGISTRATION_LC": "fordonsregistrering", "FINE_LC": "<PERSON><PERSON><PERSON>", "COURT_DATE_LC": "rättegångsdatum", "YOU_NEED_TO_BE_DRIVER_ENGINE": "Du måste vara föraren för att slå på tändningen.", "VEH_DOESNT_HAVE_THIS_SEAT": "<PERSON><PERSON> fordon har inte denna plats.", "SEAT_OCCUPIED": "Den här platsen är upptagen.", "RESETS_UI_COMMAND": "Återställer UI", "DOING_THIS_TOO_MUCH": "<PERSON> förs<PERSON><PERSON> göra detta för mycket, sluta.", "BOOK_IS_EMPTY": "Den här boken är tom!", "CREATE_BOOK_COMMAND_HELP": "[Admin] Skapa ny berättelsebok", "LACK_PERMISSION": "Du saknar nödvändiga rättigheter", "INVALID_IMG_LINK": "Ogiltig bildlänk", "INVALID_IMG_LINK_PAGE": "Ogiltig bildlänk på sida ", "STORYBOOK_ITEM_LABEL": "Bok", "FAILED_TO_ADD_ITEM": "<PERSON><PERSON><PERSON><PERSON> med att lägga till objekt!", "HUD_PRESET_DEFAULT_LABEL": "Standard", "HUD_PRESET_LEGACY_LABEL": "Legacy (Gammal)", "WELCOME_TO_PRP": "Välkommen till ProdigyRP!", "TIPS_TITLE": "Lite mer information", "APARTMENT_TIP_TITLE": "Din nya lägenhet!", "APARTMENT_TIP_TEXT": "I ditt F1-meny – eller genom att rikta 'tredje ögat' (alt) mot surfplattan på väggen i din lägenhet kan du dekorera ditt nya hem med vårt intuitiva bostadssystem. Dessutom har vi gett dig några gratismöbler i ditt välkomstpaket för att komma igång!", "PHONE_TIP_TITLE": "Din telefon", "PHONE_TIP_TEXT": "I din inventering hittar du din telefon. Högerklicka på telefonen för att öppna den och sätta in ditt SIM-kort. Uppe i hörnet i inventeringen finns fliken Verktyg där du kan utrusta din telefon.", "CIVILIAN_WORK_TIP_TITLE": "<PERSON><PERSON><PERSON>", "CIVILIAN_WORK_TIP_TEXT": "<PERSON><PERSON><PERSON><PERSON> ditt civiljobb genom att öppna din telefon och ladda ner apparna Labor eller Zoomer Driver. Båda apparna innehåller mycket ärligt arbete över hela kartan och alla ger progression och fördelar i färdighetsträdet!", "SKILL_TREE_TIP_TITLE": "Färdighetsträd", "SKILL_TREE_TIP_TEXT": "I det radiella menyn hittar du en flik som heter 'Perk-menyn'. <PERSON><PERSON>r finns ett helt gäng civila och kriminella färdighetsträd som du kan nivåupp genom att slutföra motsvarande aktivitetstyp. Du kan bara nivåa upp ett träd åt gången, men tänk på att det finns en daglig XP-gräns för att förhindra överdrivet grindande.", "NO_ARENA_CATS_TIP_TITLE": "Inga arenakatter!", "NO_ARENA_CATS_TIP_TEXT": "Vi har en ny regel för 2.0 för att bibehålla autenticitet med kläder och förhindra att spelare klär sig som toxisk RP-miljö subkulturer eller uppenbara FiveM Arena-signaler som kombinationer av skålfrisyr, k<PERSON><PERSON><PERSON><PERSON>, katt<PERSON><PERSON> och goggles.", "TICKET_RULEBREAKS_TIP_TITLE": "Öppna en ticket för <PERSON>", "TICKET_RULEBREAKS_TIP_TEXT": "Öppna en ticket på Discord för regel<PERSON> – ta inte upp det i karaktär! Att bryta karaktären är ett av de allvarligaste regelbrotten på servern och överskuggar de flesta klagomål.", "LOCKPICKS_TIP_TITLE": "Dyrkset", "LOCKPICKS_TIP_TEXT": "Det är mycket lättare att tjuvkoppla eller låsa upp en stulen bil med en skruvmejsel eller dyrk. Du kan försöka tjuvkoppla lokala bilar, men det är MYCKET svårare.", "ANIMATIONS_CLOTHING_TIP_TITLE": "Animationer och kläder", "ANIMATIONS_CLOTHING_TIP_TEXT": "I det radiella menyn hittar du visualiserade emotes som kan placeras i världen och som du kan favoritmarkera eller tilldela valfri tangentkombination för snabb användning. Dessutom har vi en meny för att ta av klädesplagg och ge dem till dina vänner... eller rånare. Var uppmärksam på denna funktion, för om du lyckas få tag på sällsynta sneakers eller kläder kan du bli tvungen att lämna över dem vid ett rån mot en rivaliserande liga eller professionell brottsling.", "FIELDS_NOT_COMPLETED": "Följande fält är inte ifyllda: %s", "STARTS_KEY": "startar", "ENDS_KEY": "slutar", "LOCATION_KEY": "plats", "TITLE_KEY": "titel", "DESCRIPTION_KEY": "beskrivning", "YOU_CAN_T_DO_THIS_WHILE_DEAD": "Du kan inte göra detta när du är död.", "YOU_CAN_T_DO_THIS_WHILE_RAGDOOLED": "Du kan inte göra detta när du är ragdolled.", "YOU_ARE_ALREADY_DOING_AN_ACTION": "<PERSON> håller redan på med en handling.", "currency": {"symbol": "SEK"}, "welcome": {"actions": {"close": "TRYCK {key}ESC{/key} FÖR ATT STÄNGA", "nextPage": "TRYCK {key}E{/key} FÖR ATT GÅ TILL NÄSTA SIDA"}, "screen": {"pageIndicator": "{currentPage}/{totalPages}"}}, "interaction": {"vehicle": {"hood": "Motorhuv", "engine": "Motor", "lights": "<PERSON><PERSON><PERSON>", "seat": "Sät<PERSON> {number}", "door": "<PERSON><PERSON><PERSON> {number}", "trunk": "Bagagelucka"}}, "sceneMenu": {"submenus": {"text": "Text", "background": "Bakgrund", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "buttons": {"editPosition": "Redigera position", "save": "Spara"}, "error": "Ett fel uppstod: {error}"}, "sceneEditor": {"text": {"sceneContent": "Sc<PERSON>nnehåll", "font": "Ty<PERSON><PERSON>tt", "fontSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Max {max})", "fontColor": "<PERSON><PERSON>nf<PERSON><PERSON>", "fontOutline": "Teckenkontur", "fontOutlineColor": "Teckenkonturfärg", "fontStyle": "Teckenstil", "placeholder": {"sceneContent": "<PERSON><PERSON>...", "fontSize": "Ange teckenstorlek..."}}, "other": {"rotation": "Rotation", "displayDistance": "Visningsavstånd", "timeVisible": "<PERSON>id synlig", "visibility": "Synlighet", "closeDistance": "Stäng avstånd"}, "background": {"background": "Bakgrund", "unknown": "Okä<PERSON>", "change": "<PERSON><PERSON>", "backgroundFill": "Bakgrundsfyllning", "backgroundColor": "Bakgrundsfärg", "backgroundOffsetX": "Bakgrundsförskjutning X", "backgroundOffsetY": "Bakgrundsförskjutning Y", "backgroundSizeX": "Bakgrundsstorlek X", "backgroundSizeY": "Bakgrundsstorlek Y"}}, "phoneBooth": {"dial": "Slå nummer", "contactList": "Kontaktlista", "delete": "<PERSON><PERSON><PERSON>", "call": "Ring", "endCall": "<PERSON><PERSON><PERSON><PERSON><PERSON> sam<PERSON>", "duration": "Varaktighet", "callStatus": {"dialing": "Ringer...", "ended": "Samtal avslutat"}, "callEnded": "Samtal avslutat"}, "ox": {"submit": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "Bekräfta"}, "jobBoard": {"task": {"active": "Aktiv", "pay": "<PERSON><PERSON><PERSON>", "time": "Tid", "location": "Plats", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "Du kan bara ha en aktiv uppgift.", "timeLeft": "<PERSON><PERSON> kvar", "dailyReward": "<PERSON><PERSON><PERSON>"}}, "injuries": {"hold": "<PERSON><PERSON><PERSON> in", "inspect": {"title": {"self": "<PERSON><PERSON> s<PERSON>", "patient": "Inspekterade patientskador"}, "actions": {"close": {"press": "<PERSON><PERSON>", "key": "ESC", "description": "<PERSON><PERSON><PERSON> att stänga"}, "camera": {"press": "<PERSON><PERSON>", "key": "C", "description": "<PERSON><PERSON><PERSON> att byta kamera"}}}, "healing": {"pleaseWait": "Vänta...", "holdToGetOut": "för att kliva ur sängen"}, "knockedDown": {"title": "<PERSON> är nedslagen", "reviveText": "Vänta på att spelare hjälper dig"}, "unconscious": {"title": "<PERSON> ka<PERSON>r är medvet<PERSON>lö<PERSON>", "respawn": "för att återuppliva din karaktär på sjukhuset", "waitToRespawn": "Vänta tills timern tar slut för att återuppliva", "callEMS": "för att ringa ambulans", "alarmEMS": "Du kan larma ambulans om", "seconds": "<PERSON><PERSON>nder"}}, "hud": {"car": {"fuel": "BRÄNSLE", "engine": "MOTOR", "belt": "BÄLTE", "flow": "FLÖDE"}}, "drugSelling": {"actionMessage": "Tryck {key}E{/key} för att bekräfta försäljningen.", "sliderValue": "{value} kr"}, "dispatch": {"reactingUnits": "Reagerande enheter", "markOnMap": "Markera på karta", "react": "<PERSON><PERSON><PERSON>", "leave": "Lämna", "accept": "Acceptera"}, "crazyTaxi": {"timeLeft": "<PERSON><PERSON> kvar", "cashBonus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashBonusValue": "{value} kr"}, "clothingMenu": {"hideClothes": "<PERSON><PERSON><PERSON><PERSON>", "saveEverything": "Spara allt", "exitWithoutSaving": "Är du säker på att du vill avsluta utan att spara?", "categories": "KATEGORIER", "available": "TILLGÄNGLIGA", "undress": "KLÄ AV", "variants": "VARIANTER"}, "pedSelector": {"useFemalePeds": "Använd kvinnliga modeller", "pedModel": "PED-MODELL", "available": "TILLGÄNGLIGA"}, "faceFeatures": {"nose": "Näsa", "eyebrows": "Ögonbryn", "cheekBones": "<PERSON><PERSON>", "jawAndChin": "<PERSON><PERSON><PERSON> och haka", "other": "<PERSON><PERSON><PERSON><PERSON>", "eyeColor": "ÖGONFÄRG"}, "faceMixSlider": {"faceMixing": "Ansiktsmixning", "thirdFaceMix": "T<PERSON>je ansiktsmix", "shape": "Form", "color": "<PERSON><PERSON><PERSON>", "both": "Båda"}, "faceSelector": {"available": "TILLGÄNGLIGA", "shape": "Form", "color": "<PERSON><PERSON><PERSON>", "face": "Ansikte", "faceIndex": "Ansikte {index}"}, "chat": {"placeholder": "Skriv ditt meddelande eller kommando...", "available": "TILLGÄNGLIGA", "staff": "PERSONAL"}, "govCardMessage": {"losSantos": "LOS SANTOS", "governmentIdentifier": "REGERINGSIDENTIFIERARE", "name": "NAMN", "stateId": "STATLIGT ID", "dob": "FÖDELSEDAG"}, "characterSelect": {"deleteConfirmation": "<PERSON>r du säker på att du vill radera {first} {last}?", "deleteWarning": "<PERSON>na <PERSON> kan inte ång<PERSON>!", "unexpectedError": "Oväntat fel uppstod! Logga in igen och försök", "anyButton": "Tryck på valfri knapp för att fortsätta...", "characterCard": {"createCharacter": "Ska<PERSON> ka<PERSON>ä<PERSON>", "characterName": "Karaktärsnamn", "lastPlayed": "Senast spelad"}}, "characterCreator": {"title": "Skapa din nya karaktär", "firstName": {"label": "Förnamn", "placeholder": "Ange din karaktärs förnamn"}, "lastName": {"label": "E<PERSON>nam<PERSON>", "placeholder": "Ange din karaktärs efternamn"}, "pronouns": {"label": "Pronomen", "options": {"heHim": "Han/<PERSON>om", "sheHer": "<PERSON>/<PERSON><PERSON>", "theyThem": "De/Dem", "other": "<PERSON><PERSON>"}}, "gender": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "origin": {"label": "Välj ursprungsland"}, "dob": {"label": "Välj födel<PERSON>atum", "placeholder": "<PERSON><PERSON><PERSON><PERSON> datum"}, "buttons": {"back": "Tillbaka", "create": "Skapa din karaktär"}, "errors": {"invalidName": "Ogiltigt namn!"}}, "cardealer": {"vehicleCarousel": {"selectVehicle": "<PERSON><PERSON><PERSON><PERSON> ett fordon", "searchPlaceholder": "<PERSON><PERSON><PERSON> efter ett fordon", "allCategories": "<PERSON><PERSON> ka<PERSON>", "allClasses": "<PERSON><PERSON> k<PERSON>er", "showAll": "Visa alla", "showInStock": "Visa i lager"}, "vehicleColors": {"title": "FÄRG", "randomTuning": "SLUMPMÄSSIG VISUELL TRIMNING"}, "vehicleStats": {"class": "KLASS", "powerScore": "KRAFTPOÄNG", "price": "PRIS", "testDrive": "PROVKÖR", "buy": "KÖP", "stats": {"power": "KRAFT", "topSpeed": "TOPPFART", "acceleration": "ACCELERATION", "braking": "BROMSAR"}}, "vehicleThumbnail": {"inStock": "I lager", "outOfStock": "Slut i lager", "class": "<PERSON><PERSON>", "price": "<PERSON><PERSON>", "limited": "BEGRÄNSAD", "hot": "POPULÄR", "currency": "{value} kr"}}, "animationMenu": {"selectAnimation": "Välj animation", "category": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> animationer", "noFound": "Inga matchande animationer hittades"}, "animationListItem": {"setBind": "Ställ in bindning", "position": "Position", "bindLabel": {"ctrl": "CTRL", "shift": "SHIFT", "alt": "ALT", "numpad": "NUM"}}, "editKeybindModal": {"title": "<PERSON><PERSON>a dina tangent<PERSON><PERSON><PERSON><PERSON>", "waiting": "Väntar...", "description": "<PERSON><PERSON><PERSON> att använda alfanumeriska tangenter måste du också hålla SHIFT/CTRL/ALT.\nDu kan använda numpad- och funktionstangenter med eller utan SHIFT/CTRL/ALT.", "warning": "Denna tangentbindning används redan för /e {emote}!\nGenom att spara tas bindningen för den motstridiga emojin bort!", "back": "Tillbaka", "clear": "Ren<PERSON>", "save": "Spara", "error": "Okänt fel uppstod!"}, "overlays": {"chesthair": "Brösthår", "bodyblemish": "Kroppsfläckar", "blush": "Rouge", "ageing": "<PERSON><PERSON><PERSON><PERSON>", "addbodyblemish": "<PERSON><PERSON><PERSON><PERSON>", "freckles": "<PERSON><PERSON><PERSON><PERSON>", "complexion": "Hy", "lipstick": "Läppstift", "makeup": "Smink", "blemish": "An<PERSON>ts<PERSON><PERSON><PERSON><PERSON><PERSON>", "facialhair": "Ansiktshår", "eyebrows": "Ögonbryn", "sundamage": "Sollskador"}, "faceFeaturesCategory": {"width": "Bredd", "peakHeight": "Topphöjd", "peakLength": "Topplängd", "boneHeight": "Benhö<PERSON>d", "peakLowering": "Toppsänkning", "boneTwist": "Benvridning", "height": "<PERSON><PERSON><PERSON><PERSON>", "length": "Längd", "boneHeight2": "Benhö<PERSON>d", "boneWidth": "Benbredd", "width2": "Bredd", "eyeOpening": "Ögonöppning", "lipThickness": "Läpptjocklek", "jawWidth": "Käkbredd", "jawLength": "Käklängd", "chinHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chinLength": "Haklängd", "chinWidth": "Hakbredd", "chinDimple": "Hakamärka", "neckThickness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "components": {"bracelet": "Armband", "hat": "Hat<PERSON>", "ear": "Örhängen", "glass": "Glasögon", "watch": "<PERSON><PERSON><PERSON>", "face": "<PERSON><PERSON>", "badge": "<PERSON><PERSON><PERSON><PERSON>", "shirt": "Tröja", "bag": "Väskor", "vests": "Västar", "hair": "<PERSON><PERSON><PERSON>", "undershirt": "Undertröja", "pants": "<PERSON><PERSON><PERSON>", "accessories": "Accessoarer", "shoes": "Skor", "arms": "Armar", "masks": "<PERSON><PERSON>", "chestHair": "Brösthår", "eyebrows": "Ögonbryn", "facialHair": "Ansiktshår"}, "clothingTitles": {"shop": "KLÄDBESÄKNINGSBUDD", "creator": "KARAKTÄRSKAPARE", "hair": "HÅRMENY"}, "categoryItems": {"face": {"face": "Ansikte", "features": "Ansiktsdrag", "overlays": "Ansiktsöverlägg", "makeup": "Smink"}, "hair": {"hair": "<PERSON><PERSON><PERSON>", "beard": "S<PERSON><PERSON>gg", "eyebrows": "Ögonbryn", "chesthair": "Brösthår"}, "body": {"blemishes": "Kroppsfläckar"}, "ped": {"ped": "Ped-v<PERSON><PERSON><PERSON><PERSON>"}, "tattoo": {"torsoTattoos": "Tatueringar överkropp", "headTattoos": "Huvudtatueringar", "leftArmTattoos": "Vänster armtatueringar", "rightArmTattoos": "<PERSON><PERSON><PERSON>", "leftLegTattoos": "<PERSON><PERSON><PERSON><PERSON> bentatueringar", "rightLegTattoos": "<PERSON><PERSON><PERSON>"}, "runwayProps": {"runwayProps": "Catwalk-rekvisita"}}, "categories": {"face": "Ansikte", "hair": "<PERSON><PERSON><PERSON>", "clothes": "<PERSON><PERSON><PERSON><PERSON>", "body": "K<PERSON><PERSON>", "tattoos": "<PERSON><PERSON><PERSON><PERSON>", "ped": "Ped", "runwayProps": "Catwalk-rekvisita"}, "eyeColors": {"green": "<PERSON><PERSON><PERSON><PERSON>", "emerald": "Smaragdgrön", "lightBlue": "Ljusblå", "oceanBlue": "Havsblå", "lightBrown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkBrown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hazel": "<PERSON><PERSON><PERSON><PERSON>", "darkGray": "Mörkgrå", "lightGray": "Ljusgrå", "pink": "<PERSON>", "yellow": "Gul", "purple": "<PERSON>", "blackout": "Mörkläggning", "shadesOfGray": "Gråskalor", "tequilaSunrise": "Tequila Sunrise", "atomic": "Atomisk", "warp": "<PERSON><PERSON><PERSON>", "eCola": "ECola", "spaceRanger": "<PERSON><PERSON><PERSON><PERSON>", "yingYang": "<PERSON>", "bullseye": "Måltavla", "lizard": "Ödl<PERSON>", "dragon": "<PERSON>", "extraTerrestrial": "Utomjordisk", "goat": "Get", "smiley": "<PERSON><PERSON>", "possessed": "Besatt", "demon": "Demon", "infected": "Infekterad", "alien": "Alien", "undead": "<PERSON><PERSON><PERSON><PERSON>", "zombie": "Zombie"}, "overlayColor": {"primaryColor": "Primärfärg", "secondaryColor": "Sekundärfärg"}, "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yes": "Godkä<PERSON>"}