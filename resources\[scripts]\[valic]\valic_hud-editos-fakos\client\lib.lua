-- ██╗   ██╗ █████╗ ██╗     ██╗ ██████╗    ██╗     ██╗██████╗ 
-- ██║   ██║██╔══██╗██║     ██║██╔════╝    ██║     ██║██╔══██╗
-- ██║   ██║███████║██║     ██║██║         ██║     ██║██████╔╝
-- ╚██╗ ██╔╝██╔══██║██║     ██║██║         ██║     ██║██╔══██╗
--  ╚████╔╝ ██║  ██║███████╗██║╚██████╗    ███████╗██║██████╔╝
--   ╚═══╝  ╚═╝  ╚═╝╚══════╝╚═╝ ╚═════╝    ╚══════╝╚═╝╚═════╝ 
-- 
-- Valic HUD - Client Library
-- Author: Valic
-- Description: Helper functions and utilities for client-side

local QBCore = exports['qb-core']:GetCoreObject()

-- Library object
ValicHUD = {}
ValicHUD.Utils = {}
ValicHUD.UI = {}
ValicHUD.Vehicle = {}
ValicHUD.Player = {}

-- Utility functions
function ValicHUD.Utils.Round(value, numDecimalPlaces)
    if numDecimalPlaces then
        local power = 10^numDecimalPlaces
        return math.floor((value * power) + 0.5) / power
    else
        return math.floor(value + 0.5)
    end
end

function ValicHUD.Utils.FormatNumber(num)
    if num >= 1000000 then
        return string.format("%.1fM", num / 1000000)
    elseif num >= 1000 then
        return string.format("%.1fK", num / 1000)
    else
        return tostring(num)
    end
end

function ValicHUD.Utils.GetStreetName()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local streetHash, crossingHash = GetStreetNameAtCoord(playerCoords.x, playerCoords.y, playerCoords.z)
    local streetName = GetStreetNameFromHashKey(streetHash)
    local crossingName = GetStreetNameFromHashKey(crossingHash)
    
    if crossingName ~= "" then
        return streetName .. " / " .. crossingName
    else
        return streetName
    end
end

function ValicHUD.Utils.GetZoneName()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local zoneHash = GetNameOfZone(playerCoords.x, playerCoords.y, playerCoords.z)
    return GetLabelText(zoneHash)
end

function ValicHUD.Utils.GetCompassDirection(heading)
    if heading >= 337.5 or heading < 22.5 then
        return "N"
    elseif heading >= 22.5 and heading < 67.5 then
        return "NE"
    elseif heading >= 67.5 and heading < 112.5 then
        return "E"
    elseif heading >= 112.5 and heading < 157.5 then
        return "SE"
    elseif heading >= 157.5 and heading < 202.5 then
        return "S"
    elseif heading >= 202.5 and heading < 247.5 then
        return "SW"
    elseif heading >= 247.5 and heading < 292.5 then
        return "W"
    elseif heading >= 292.5 and heading < 337.5 then
        return "NW"
    end
end

-- UI functions
function ValicHUD.UI.SendMessage(action, data)
    SendNUIMessage({
        action = action,
        data = data or {}
    })
end

function ValicHUD.UI.ShowNotification(message, type, duration)
    ValicHUD.UI.SendMessage('showNotification', {
        message = message,
        type = type or 'info',
        duration = duration or 5000
    })
end

function ValicHUD.UI.ShowProgress(label, duration, useWhileDead, canCancel, disableControls, animation, prop, propTwo)
    ValicHUD.UI.SendMessage('showProgress', {
        label = label,
        duration = duration,
        useWhileDead = useWhileDead or false,
        canCancel = canCancel or false,
        disableControls = disableControls or {},
        animation = animation,
        prop = prop,
        propTwo = propTwo
    })
end

function ValicHUD.UI.HideProgress()
    ValicHUD.UI.SendMessage('hideProgress')
end

function ValicHUD.UI.ShowInput(header, submitText, inputs)
    ValicHUD.UI.SendMessage('showInput', {
        header = header,
        submitText = submitText,
        inputs = inputs
    })
    SetNuiFocus(true, true)
end

function ValicHUD.UI.ShowConfirm(header, message, confirmText, cancelText)
    ValicHUD.UI.SendMessage('showConfirm', {
        header = header,
        message = message,
        confirmText = confirmText or 'Confirm',
        cancelText = cancelText or 'Cancel'
    })
    SetNuiFocus(true, true)
end

-- Vehicle functions
function ValicHUD.Vehicle.GetVehicleData(vehicle)
    if not vehicle or vehicle == 0 then return nil end
    
    local speed = GetEntitySpeed(vehicle)
    local rpm = GetVehicleCurrentRpm(vehicle)
    local fuel = GetVehicleFuelLevel(vehicle)
    local engineHealth = GetVehicleEngineHealth(vehicle)
    local bodyHealth = GetVehicleBodyHealth(vehicle)
    local gear = GetVehicleCurrentGear(vehicle)
    
    return {
        speed = speed,
        rpm = rpm,
        fuel = fuel,
        engineHealth = engineHealth,
        bodyHealth = bodyHealth,
        gear = gear,
        maxSpeed = GetVehicleModelMaxSpeed(GetEntityModel(vehicle))
    }
end

function ValicHUD.Vehicle.IsEngineOn(vehicle)
    return GetIsVehicleEngineRunning(vehicle)
end

function ValicHUD.Vehicle.GetVehicleClass(vehicle)
    return GetVehicleClass(vehicle)
end

-- Player functions
function ValicHUD.Player.GetPlayerData()
    return QBCore.Functions.GetPlayerData()
end

function ValicHUD.Player.GetPlayerStats()
    local playerData = ValicHUD.Player.GetPlayerData()
    if not playerData then return nil end
    
    return {
        health = GetEntityHealth(PlayerPedId()),
        armor = GetPedArmour(PlayerPedId()),
        hunger = playerData.metadata and playerData.metadata.hunger or 100,
        thirst = playerData.metadata and playerData.metadata.thirst or 100,
        stress = playerData.metadata and playerData.metadata.stress or 0,
        money = playerData.money or {}
    }
end

function ValicHUD.Player.GetWeaponData()
    local playerPed = PlayerPedId()
    local weapon = GetSelectedPedWeapon(playerPed)
    
    if weapon == GetHashKey("WEAPON_UNARMED") then
        return nil
    end
    
    local ammo = GetAmmoInPedWeapon(playerPed, weapon)
    local maxAmmo = GetMaxAmmoInClip(playerPed, weapon, true)
    
    return {
        weapon = weapon,
        ammo = ammo,
        maxAmmo = maxAmmo,
        weaponName = QBCore.Shared.Weapons[weapon] and QBCore.Shared.Weapons[weapon].label or "Unknown"
    }
end

-- Location functions
function ValicHUD.Player.GetLocationData()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local heading = GetEntityHeading(playerPed)
    
    return {
        coords = playerCoords,
        heading = heading,
        street = ValicHUD.Utils.GetStreetName(),
        zone = ValicHUD.Utils.GetZoneName(),
        direction = ValicHUD.Utils.GetCompassDirection(heading)
    }
end

-- Time and weather functions
function ValicHUD.Utils.GetGameTime()
    local hour = GetClockHours()
    local minute = GetClockMinutes()
    return {
        hour = hour,
        minute = minute,
        formatted = string.format("%02d:%02d", hour, minute)
    }
end

function ValicHUD.Utils.GetWeather()
    local weather = GetPrevWeatherTypeHashName()
    return weather
end

-- Export the library
_G.ValicHUD = ValicHUD
