-- ██╗███╗   ██╗██████╗ ██╗   ██╗████████╗
-- ██║████╗  ██║██╔══██╗██║   ██║╚══██╔══╝
-- ██║██╔██╗ ██║██████╔╝██║   ██║   ██║   
-- ██║██║╚██╗██║██╔═══╝ ██║   ██║   ██║   
-- ██║██║ ╚████║██║     ╚██████╔╝   ██║   
-- ╚═╝╚═╝  ╚═══╝╚═╝      ╚═════╝    ╚═╝   
-- 
-- Valic HUD - Input Module
-- Author: Valic
-- Description: Advanced input dialog system

local QBCore = exports['qb-core']:GetCoreObject()

-- Input state
local inputActive = false
local inputCallback = nil

-- Show input dialog
function ShowInput(data)
    if inputActive then
        return false
    end
    
    inputActive = true
    
    -- Validate required data
    if not data.header or not data.inputs then
        inputActive = false
        return false
    end
    
    -- Set defaults
    data.submitText = data.submitText or 'Submit'
    data.cancelText = data.cancelText or 'Cancel'
    
    -- Send to UI
    SendNUIMessage({
        action = 'showInput',
        data = data
    })
    
    SetNuiFocus(true, true)
    return true
end

-- Hide input dialog
function HideInput()
    if not inputActive then return end
    
    inputActive = false
    inputCallback = nil
    
    SendNUIMessage({
        action = 'hideInput'
    })
    
    SetNuiFocus(false, false)
end

-- Events
RegisterNetEvent('valic_hud:client:input', function(data, cb)
    inputCallback = cb
    ShowInput(data)
end)

-- NUI Callbacks
RegisterNUICallback('inputSubmit', function(data, cb)
    HideInput()
    
    if inputCallback then
        inputCallback(data.values)
        inputCallback = nil
    end
    
    TriggerEvent('valic_hud:client:inputSubmitted', data.values)
    cb('ok')
end)

RegisterNUICallback('inputCancel', function(data, cb)
    HideInput()
    
    if inputCallback then
        inputCallback(nil)
        inputCallback = nil
    end
    
    TriggerEvent('valic_hud:client:inputCancelled')
    cb('ok')
end)

-- QBCore input override
local originalInput = lib.inputDialog
lib.inputDialog = function(header, rows, options)
    local inputs = {}
    
    for i, row in ipairs(rows) do
        local input = {
            text = row.label or row[1],
            name = row.name or 'input_' .. i,
            type = row.type or 'text',
            isRequired = row.required or false
        }
        
        if row.type == 'select' then
            input.options = row.options
        elseif row.type == 'number' then
            input.min = row.min
            input.max = row.max
        elseif row.type == 'text' or row.type == 'password' then
            input.placeholder = row.placeholder
            input.maxlength = row.maxlength
        end
        
        table.insert(inputs, input)
    end
    
    local promise = promise.new()
    
    local data = {
        header = header,
        inputs = inputs,
        submitText = options and options.submitText or 'Submit',
        cancelText = options and options.cancelText or 'Cancel'
    }
    
    TriggerEvent('valic_hud:client:input', data, function(result)
        promise:resolve(result)
    end)
    
    return Citizen.Await(promise)
end

-- Specialized input functions
function ShowTextInput(header, placeholder, maxLength, required)
    local data = {
        header = header,
        inputs = {
            {
                text = header,
                name = 'text',
                type = 'text',
                placeholder = placeholder or '',
                maxlength = maxLength or 100,
                isRequired = required or false
            }
        }
    }
    
    local promise = promise.new()
    TriggerEvent('valic_hud:client:input', data, function(result)
        if result then
            promise:resolve(result.text)
        else
            promise:resolve(nil)
        end
    end)
    
    return Citizen.Await(promise)
end

function ShowNumberInput(header, min, max, required)
    local data = {
        header = header,
        inputs = {
            {
                text = header,
                name = 'number',
                type = 'number',
                min = min or 0,
                max = max or 999999,
                isRequired = required or false
            }
        }
    }
    
    local promise = promise.new()
    TriggerEvent('valic_hud:client:input', data, function(result)
        if result then
            promise:resolve(tonumber(result.number))
        else
            promise:resolve(nil)
        end
    end)
    
    return Citizen.Await(promise)
end

function ShowSelectInput(header, options, required)
    local data = {
        header = header,
        inputs = {
            {
                text = header,
                name = 'select',
                type = 'select',
                options = options,
                isRequired = required or false
            }
        }
    }
    
    local promise = promise.new()
    TriggerEvent('valic_hud:client:input', data, function(result)
        if result then
            promise:resolve(result.select)
        else
            promise:resolve(nil)
        end
    end)
    
    return Citizen.Await(promise)
end

function ShowPasswordInput(header, placeholder, maxLength, required)
    local data = {
        header = header,
        inputs = {
            {
                text = header,
                name = 'password',
                type = 'password',
                placeholder = placeholder or '',
                maxlength = maxLength or 50,
                isRequired = required or false
            }
        }
    }
    
    local promise = promise.new()
    TriggerEvent('valic_hud:client:input', data, function(result)
        if result then
            promise:resolve(result.password)
        else
            promise:resolve(nil)
        end
    end)
    
    return Citizen.Await(promise)
end

-- Multi-input dialog
function ShowMultiInput(header, inputs, submitText, cancelText)
    local data = {
        header = header,
        inputs = inputs,
        submitText = submitText or 'Submit',
        cancelText = cancelText or 'Cancel'
    }
    
    local promise = promise.new()
    TriggerEvent('valic_hud:client:input', data, function(result)
        promise:resolve(result)
    end)
    
    return Citizen.Await(promise)
end

-- Exports
exports('showInput', ShowInput)
exports('hideInput', HideInput)
exports('showTextInput', ShowTextInput)
exports('showNumberInput', ShowNumberInput)
exports('showSelectInput', ShowSelectInput)
exports('showPasswordInput', ShowPasswordInput)
exports('showMultiInput', ShowMultiInput)
exports('isInputActive', function()
    return inputActive
end)
