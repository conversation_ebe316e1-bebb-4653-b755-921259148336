-- Init Locales
lib.locale()

-- Weapons in this array will not show the ammo count in the hud.
HudConfig = {
    ExcludedWeapons = {
        [`WEAPON_SPEEDRADAR`] = true,
        [`WEAPON_PETROLCAN`] = true,
        [`weapon_scanner`] = true,
        [`WEAPON_PRESSUREWASHER`] = true,
        [`WEAPON_CHAINSAW`] = true,
        [`WEAPON_DRILL`] = true,
    },
    ForceHideAmmoWeapons = {
        [`WEAPON_GPSLAUNCHER`] = true,
    }
}

Presets = {
    DEFAULT = {
        Label = locale("HUD_PRESET_DEFAULT_LABEL"),

        Settings = {
            mapFormat = "circle",
            mapAxis = {
                x = 0.0,
                y = -0.02
            },

            barsLocation = "horizontal",
            barsAlignment = "custom",
            barsStyle = "fill",
            barsAxis = {
                x = 1,
                y = 1
            },

            textAlignment = "center",
            locationAlignment = "center",
            locationAxis = {
                x = 0,
                y = 0
            },
            locationOnFoot = false,

            speedometerStyle = "new"
        }
    },
    LEGACY = {
        Label = locale("HUD_PRESET_LEGACY_LABEL"),

        Settings = {
            mapFormat = "rectangular",
            mapAxis = {
                x = 0.0,
                y = 0.03,
            },

            barsLocation = "horizontal",
            barsAlignment = "center",
            barsStyle = "fill",
            barsAxis = {
                x = 1,
                y = 1,
            },

            textAlignment = "left",
            locationAlignment = "custom",
            locationAxis = {
                x = 16,
                y = 95,
            },
            locationOnFoot = true,

            speedometerStyle = "old"
        }
    }
}

WelcomeScreens = {
    {
        imageURL = "img/welcome/welcome_1.png",
        isVideo = false,
        title = locale("WELCOME_TO_PRP"),
        parts = {
            {
                title = locale("APARTMENT_TIP_TITLE"),
                text = locale("APARTMENT_TIP_TEXT")
            },
            {
                title = locale("PHONE_TIP_TITLE"),
                text = locale("PHONE_TIP_TEXT")
            },
            {
                title = locale("CIVILIAN_WORK_TIP_TITLE"),
                text = locale("CIVILIAN_WORK_TIP_TEXT")
            },
            {
                title = locale("SKILL_TREE_TIP_TITLE"),
                text = locale("SKILL_TREE_TIP_TEXT")
            }
        }
    },
    {
        imageURL = "img/welcome/welcome_2.png",
        isVideo = false,
        title = locale("TIPS_TITLE"),
        parts = {
            {
                title = locale("NO_ARENA_CATS_TIP_TITLE"),
                text = locale("NO_ARENA_CATS_TIP_TEXT")
            },
            {
                title = locale("TICKET_RULEBREAKS_TIP_TITLE"),
                text = locale("TICKET_RULEBREAKS_TIP_TEXT")
            },
            {
                title = locale("LOCKPICKS_TIP_TITLE"),
                text = locale("LOCKPICKS_TIP_TEXT")
            },
            {
                title = locale("ANIMATIONS_CLOTHING_TIP_TITLE"),
                text = locale("ANIMATIONS_CLOTHING_TIP_TEXT")
            }
        }
    }
}

ATC = {
    AircraftAlertTimeoutSeconds = 30 -- Time to wait to replay new aircraft alert sound for a previously observed aircraft
}
