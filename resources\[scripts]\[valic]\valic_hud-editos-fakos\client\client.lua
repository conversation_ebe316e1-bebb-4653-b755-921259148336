-- ██╗   ██╗ █████╗ ██╗     ██╗ ██████╗    ██╗  ██╗██╗   ██╗██████╗ 
-- ██║   ██║██╔══██╗██║     ██║██╔════╝    ██║  ██║██║   ██║██╔══██╗
-- ██║   ██║███████║██║     ██║██║         ███████║██║   ██║██║  ██║
-- ╚██╗ ██╔╝██╔══██║██║     ██║██║         ██╔══██║██║   ██║██║  ██║
--  ╚████╔╝ ██║  ██║███████╗██║╚██████╗    ██║  ██║╚██████╔╝██████╔╝
--   ╚═══╝  ╚═╝  ╚═╝╚══════╝╚═╝ ╚═════╝    ╚═╝  ╚═╝ ╚═════╝ ╚═════╝ 
-- 
-- Valic HUD - Main Client Script
-- Author: Valic
-- Description: Main client-side initialization and core functionality

local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = QBCore.Functions.GetPlayerData()
local isLoggedIn = LocalPlayer.state.isLoggedIn

-- Global variables
local hudVisible = true
local hudSettings = {}
local currentVehicle = nil
local isInVehicle = false
local playerPed = PlayerPedId()

-- Initialize HUD
CreateThread(function()
    while not LocalPlayer.state.isLoggedIn do
        Wait(1000)
    end
    
    -- Wait for UI to be ready
    while not GetResourceState('valic_hud-editos-fakos') == 'started' do
        Wait(100)
    end
    
    -- Initialize HUD components
    TriggerEvent('valic_hud:client:initializeHUD')
    
    -- Load player settings
    TriggerServerEvent('valic_hud:server:loadSettings')
    
    print("^2[Valic HUD]^7 Successfully initialized!")
end)

-- Player data update
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    isLoggedIn = true
    TriggerEvent('valic_hud:client:playerLoaded')
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
    isLoggedIn = false
    TriggerEvent('valic_hud:client:playerUnloaded')
end)

RegisterNetEvent('QBCore:Player:SetPlayerData', function(val)
    PlayerData = val
    TriggerEvent('valic_hud:client:updatePlayerData', PlayerData)
end)

-- Vehicle events
CreateThread(function()
    while true do
        Wait(1000)
        
        if isLoggedIn then
            playerPed = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            
            if vehicle ~= 0 and vehicle ~= currentVehicle then
                currentVehicle = vehicle
                isInVehicle = true
                TriggerEvent('valic_hud:client:enteredVehicle', vehicle)
            elseif vehicle == 0 and isInVehicle then
                TriggerEvent('valic_hud:client:exitedVehicle', currentVehicle)
                currentVehicle = nil
                isInVehicle = false
            end
        end
    end
end)

-- HUD visibility toggle
RegisterCommand('togglehud', function()
    hudVisible = not hudVisible
    SendNUIMessage({
        action = 'toggleHUD',
        visible = hudVisible
    })
    QBCore.Functions.Notify(hudVisible and 'HUD Enabled' or 'HUD Disabled', 'primary')
end)

-- Reset UI command
RegisterCommand('resetui', function()
    SendNUIMessage({
        action = 'resetUI'
    })
    QBCore.Functions.Notify('UI Reset', 'primary')
end)

-- Key mappings
RegisterKeyMapping('togglehud', locale('KEYBIND_TOGGLE_HUD'), 'keyboard', 'F2')
RegisterKeyMapping('showinteraction', locale('KEYBIND_SHOW_INTERACTION_MENU'), 'keyboard', 'F1')
RegisterKeyMapping('showids', locale('KEYBIND_HUD_SHOW_IDS'), 'keyboard', 'LALT')

-- NUI Callbacks
RegisterNUICallback('hideFrame', function(data, cb)
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('saveSettings', function(data, cb)
    hudSettings = data.settings
    TriggerServerEvent('valic_hud:server:saveSettings', hudSettings)
    cb('ok')
end)

RegisterNUICallback('getSettings', function(data, cb)
    cb(hudSettings)
end)

-- Exports
exports('getHudSettings', function()
    return hudSettings
end)

exports('setHudSettings', function(settings)
    hudSettings = settings
    SendNUIMessage({
        action = 'updateSettings',
        settings = settings
    })
end)

exports('toggleHUD', function(visible)
    if visible ~= nil then
        hudVisible = visible
    else
        hudVisible = not hudVisible
    end
    
    SendNUIMessage({
        action = 'toggleHUD',
        visible = hudVisible
    })
end)

exports('isHudVisible', function()
    return hudVisible
end)

-- Events
RegisterNetEvent('valic_hud:client:updateSettings', function(settings)
    hudSettings = settings
    SendNUIMessage({
        action = 'updateSettings',
        settings = settings
    })
end)

RegisterNetEvent('valic_hud:client:initializeHUD', function()
    -- Initialize all HUD components
    TriggerEvent('valic_hud:client:setupHUD')
    TriggerEvent('valic_hud:client:setupVehicleHUD')
    TriggerEvent('valic_hud:client:setupNotifications')
    TriggerEvent('valic_hud:client:setupInteractions')
end)

-- Cleanup
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        SendNUIMessage({
            action = 'cleanup'
        })
    end
end)
